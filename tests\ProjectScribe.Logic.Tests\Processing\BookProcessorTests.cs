using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Audio;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Parsing;
using ProjectScribe.Logic.Parsing.Models;
using ProjectScribe.Logic.Processing;
using ProjectScribe.Logic.State;
using ProjectScribe.Logic.State.Entities;
using ProjectScribe.Logic.Text;
using ProjectScribe.Logic.TTS;
using Xunit;

namespace ProjectScribe.Logic.Tests.Processing
{
    /// <summary>
    /// Unit tests for the BookProcessor class.
    /// Tests orchestration logic, error handling, and state management.
    /// </summary>
    public class BookProcessorTests
    {
        private readonly Mock<IConfigurationManager> _mockConfigManager;
        private readonly Mock<ILogger<BookProcessor>> _mockLogger;
        private readonly Mock<IStateManager> _mockStateManager;
        private readonly Mock<IInputParser> _mockInputParser;
        private readonly Mock<ITextProcessor> _mockTextProcessor;
        private readonly Mock<IStyleProcessor> _mockStyleProcessor;
        private readonly Mock<ITtsClient> _mockTtsClient;
        private readonly Mock<IAudioPostProcessor> _mockAudioPostProcessor;
        private readonly BookProcessor _bookProcessor;
        private readonly AppSettings _testAppSettings;

        public BookProcessorTests()
        {
            _mockConfigManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<BookProcessor>>();
            _mockStateManager = new Mock<IStateManager>();
            _mockInputParser = new Mock<IInputParser>();
            _mockTextProcessor = new Mock<ITextProcessor>();
            _mockStyleProcessor = new Mock<IStyleProcessor>();
            _mockTtsClient = new Mock<ITtsClient>();
            _mockAudioPostProcessor = new Mock<IAudioPostProcessor>();

            _testAppSettings = new AppSettings
            {
                BaseLibraryDirectory = @"C:\test\library",
                PrepareDirectoryName = "prepare",
                StyledDirectoryName = "styled",
                WavDirectoryName = "wav",
                OutputDirectoryName = "output",
                TextBlockTargetCharCount = 1800,
                MaxConcurrentTtsThreads = 2,
                MaxTtsRetries = 3,
                MaxAudioBatchDurationMinutes = 60,
                GeminiTtsFirstVoiceName = "TestVoice1",
                GeminiTtsSecondVoiceName = "TestVoice2",
                StylePreset = "STYLE:\nTest style preset\nTEXT:\n\n"
            };

            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(_testAppSettings);

            _bookProcessor = new BookProcessor(
                _mockConfigManager.Object,
                _mockLogger.Object,
                _mockStateManager.Object,
                _mockInputParser.Object,
                _mockTextProcessor.Object,
                _mockStyleProcessor.Object,
                _mockTtsClient.Object,
                _mockAudioPostProcessor.Object);
        }

        [Fact]
        public async Task ProcessBookAsync_WithNullFilePath_ThrowsArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _bookProcessor.ProcessBookAsync(null!));
        }

        [Fact]
        public async Task ProcessBookAsync_WithEmptyFilePath_ThrowsArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _bookProcessor.ProcessBookAsync(""));
        }

        [Fact]
        public async Task ProcessBookAsync_WithNonExistentFile_ThrowsArgumentException()
        {
            // Arrange
            var nonExistentPath = @"C:\nonexistent\file.epub";

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _bookProcessor.ProcessBookAsync(nonExistentPath));
        }

        [Fact]
        public async Task ProcessBookAsync_WithParsingFailure_UpdatesBookStatusToFailed()
        {
            // Arrange
            var testFilePath = CreateTestFile();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ThrowsAsync(new InvalidOperationException("Parsing failed"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _bookProcessor.ProcessBookAsync(testFilePath));

            // Verify book status was updated to failed
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(
                It.IsAny<string>(),
                BookProcessStatus.Failed,
                "Error",
                It.IsAny<string>()), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessBookAsync_WithNoChapters_UpdatesBookStatusToFailed()
        {
            // Arrange
            var testFilePath = CreateTestFile();
            var emptyChapters = new List<ChapterContent>();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(emptyChapters);

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(
                It.IsAny<string>(),
                BookProcessStatus.Failed,
                "Parsing",
                "No chapters found in the book"), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessBookAsync_WithValidBook_ProcessesSuccessfully()
        {
            // Arrange
            var testFilePath = CreateTestFile();
            var chapters = CreateTestChapters();
            var textBlocks = CreateTestTextBlocks();
            var pendingBlocks = CreateTestTextBlockStates();
            var successfulBlocks = CreateSuccessfulTextBlockStates();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(chapters);

            _mockTextProcessor.Setup(x => x.ProcessChapter(It.IsAny<ChapterContent>(), It.IsAny<string>()))
                .Returns(textBlocks);

            _mockStateManager.Setup(x => x.GetTextBlocksForBookByStatusAsync(It.IsAny<string>(), TextBlockStatus.PendingStyleEnhancement))
                .ReturnsAsync(pendingBlocks);

            _mockStateManager.Setup(x => x.GetPendingTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(pendingBlocks);

            _mockStateManager.Setup(x => x.GetTextBlocksForBookByStatusAsync(It.IsAny<string>(), TextBlockStatus.TTS_Success))
                .ReturnsAsync(successfulBlocks);

            var mockAudioStream = new MemoryStream(new byte[] { 0x52, 0x49, 0x46, 0x46, 0x24, 0x00, 0x00, 0x00 }); // RIFF header
            _mockTtsClient.Setup(x => x.GetTtsAudioAsync(It.IsAny<string>()))
                .ReturnsAsync(mockAudioStream);

            _mockAudioPostProcessor.Setup(x => x.ProcessBatchAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
                .ReturnsAsync(@"C:\test\output\test_book_part001.opus");

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert
            _mockStateManager.Verify(x => x.InitializeBookAsync(It.IsAny<string>(), testFilePath), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.ProcessingText, "Parsing", null), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.ProcessingText, "TextProcessing", null), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.ProcessingStyleEnhancement, "StyleEnhancement", null), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.ProcessingAudio, "TTS", null), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.ProcessingAudio, "AudioPostProcessing", null), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.Completed, "AudioPostProcessing_Complete", null), Times.Once);

            // Verify audio post-processing was called
            _mockAudioPostProcessor.Verify(x => x.ProcessBatchAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessBookAsync_WithNoSuccessfulWavFiles_SkipsAudioPostProcessing()
        {
            // Arrange
            var testFilePath = CreateTestFile();
            var chapters = CreateTestChapters();
            var textBlocks = CreateTestTextBlocks();
            var pendingBlocks = CreateTestTextBlockStates();
            var emptySuccessfulBlocks = new List<TextBlockState>();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(chapters);

            _mockTextProcessor.Setup(x => x.ProcessChapter(It.IsAny<ChapterContent>(), It.IsAny<string>()))
                .Returns(textBlocks);

            _mockStateManager.Setup(x => x.GetPendingTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(pendingBlocks);

            _mockStateManager.Setup(x => x.GetTextBlocksForBookByStatusAsync(It.IsAny<string>(), TextBlockStatus.TTS_Success))
                .ReturnsAsync(emptySuccessfulBlocks);

            var mockAudioStream = new MemoryStream(new byte[] { 0x52, 0x49, 0x46, 0x46, 0x24, 0x00, 0x00, 0x00 }); // RIFF header
            _mockTtsClient.Setup(x => x.GetTtsAudioAsync(It.IsAny<string>()))
                .ReturnsAsync(mockAudioStream);

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.ProcessingAudio, "AudioPostProcessing", null), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.Completed, "AudioPostProcessing_Complete", null), Times.Once);

            // Verify audio post-processing was NOT called
            _mockAudioPostProcessor.Verify(x => x.ProcessBatchAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()), Times.Never);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessBookAsync_WithAudioPostProcessingFailure_ContinuesProcessing()
        {
            // Arrange
            var testFilePath = CreateTestFile();
            var chapters = CreateTestChapters();
            var textBlocks = CreateTestTextBlocks();
            var pendingBlocks = CreateTestTextBlockStates();
            var successfulBlocks = CreateSuccessfulTextBlockStates();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(chapters);

            _mockTextProcessor.Setup(x => x.ProcessChapter(It.IsAny<ChapterContent>(), It.IsAny<string>()))
                .Returns(textBlocks);

            _mockStateManager.Setup(x => x.GetPendingTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(pendingBlocks);

            _mockStateManager.Setup(x => x.GetTextBlocksForBookByStatusAsync(It.IsAny<string>(), TextBlockStatus.TTS_Success))
                .ReturnsAsync(successfulBlocks);

            var mockAudioStream = new MemoryStream(new byte[] { 0x52, 0x49, 0x46, 0x46, 0x24, 0x00, 0x00, 0x00 }); // RIFF header
            _mockTtsClient.Setup(x => x.GetTtsAudioAsync(It.IsAny<string>()))
                .ReturnsAsync(mockAudioStream);

            // Setup audio post-processor to return null (failure)
            _mockAudioPostProcessor.Setup(x => x.ProcessBatchAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
                .ReturnsAsync((string?)null);

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert - processing should still complete even if audio post-processing fails
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.Completed, "AudioPostProcessing_Complete", null), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessBookAsync_WithPermanentlyFailedBlocks_ResetsBlocksOnRestart()
        {
            // Arrange
            var testFilePath = CreateTestFile();
            var chapters = CreateTestChapters();
            var textBlocks = CreateTestTextBlocks();
            var pendingBlocks = CreateTestTextBlockStates();
            var successfulBlocks = CreateSuccessfulTextBlockStates();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(chapters);

            _mockTextProcessor.Setup(x => x.ProcessChapter(It.IsAny<ChapterContent>(), It.IsAny<string>()))
                .Returns(textBlocks);

            _mockStateManager.Setup(x => x.GetPendingTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(pendingBlocks);

            _mockStateManager.Setup(x => x.GetTextBlocksForBookByStatusAsync(It.IsAny<string>(), TextBlockStatus.TTS_Success))
                .ReturnsAsync(successfulBlocks);

            // Setup reset permanently failed blocks to return 2 (simulating 2 blocks were reset)
            _mockStateManager.Setup(x => x.ResetPermanentlyFailedTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(2);

            var mockAudioStream = new MemoryStream(new byte[] { 0x52, 0x49, 0x46, 0x46, 0x24, 0x00, 0x00, 0x00 }); // RIFF header
            _mockTtsClient.Setup(x => x.GetTtsAudioAsync(It.IsAny<string>()))
                .ReturnsAsync(mockAudioStream);

            _mockAudioPostProcessor.Setup(x => x.ProcessBatchAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
                .ReturnsAsync(@"C:\test\output\test_book_part001.opus");

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert
            _mockStateManager.Verify(x => x.ResetPermanentlyFailedTextBlocksAsync(It.IsAny<string>()), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(It.IsAny<string>(), BookProcessStatus.Completed, "AudioPostProcessing_Complete", null), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessTtsAsync_WithPermanentlyFailedBlocksAfterProcessing_ThrowsException()
        {
            // Arrange
            var testFilePath = CreateTestFile();
            var chapters = CreateTestChapters();
            var textBlocks = CreateTestTextBlocks();
            var pendingBlocks = CreateTestTextBlockStates();

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(chapters);

            _mockTextProcessor.Setup(x => x.ProcessChapter(It.IsAny<ChapterContent>(), It.IsAny<string>()))
                .Returns(textBlocks);

            _mockStateManager.Setup(x => x.GetPendingTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(pendingBlocks);

            // Setup no permanently failed blocks at start
            _mockStateManager.Setup(x => x.ResetPermanentlyFailedTextBlocksAsync(It.IsAny<string>()))
                .ReturnsAsync(0);

            // Setup permanently failed blocks after TTS processing (simulating blocks that failed during this run)
            // The check happens after Task.WhenAll completes, so we need to return 2 permanently failed blocks
            _mockStateManager.Setup(x => x.GetTextBlockCountByStatusAsync(It.IsAny<string>(), TextBlockStatus.TTS_FailedPermanent))
                .ReturnsAsync(2);

            // Setup TTS client to return null (simulating failure)
            _mockTtsClient.Setup(x => x.GetTtsAudioAsync(It.IsAny<string>()))
                .ReturnsAsync((Stream?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _bookProcessor.ProcessBookAsync(testFilePath));

            Assert.Contains("TTS processing failed: 2 text blocks failed permanently", exception.Message);
            Assert.Contains("Application will stop to prevent proceeding to next stage", exception.Message);

            // Verify book status was updated to failed
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(
                It.IsAny<string>(),
                BookProcessStatus.Failed,
                "TTS",
                It.Is<string>(msg => msg.Contains("TTS processing failed"))), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        private string CreateTestFile()
        {
            var tempPath = Path.GetTempFileName();
            File.WriteAllText(tempPath, "test content");
            return tempPath;
        }

        private List<ChapterContent> CreateTestChapters()
        {
            return new List<ChapterContent>
            {
                new ChapterContent
                {
                    ChapterNumber = 1,
                    Title = "Chapter 1",
                    TextContent = "This is the content of chapter 1."
                },
                new ChapterContent
                {
                    ChapterNumber = 2,
                    Title = "Chapter 2",
                    TextContent = "This is the content of chapter 2."
                }
            };
        }

        private List<TextBlock> CreateTestTextBlocks()
        {
            return new List<TextBlock>
            {
                new TextBlock
                {
                    BlockId = "block1",
                    BookId = "test-book",
                    SequenceInChapter = 1,
                    OriginalChapterTitle = "Chapter 1",
                    SanitizedText = "This is the content of chapter 1."
                }
            };
        }

        private List<TextBlockState> CreateTestTextBlockStates()
        {
            return new List<TextBlockState>
            {
                new TextBlockState
                {
                    TextBlockId = "block1",
                    BookId = "test-book",
                    BlockSequenceNumber = 1,
                    TextFilePath = @"C:\test\prepare\0001.txt",
                    Status = TextBlockStatus.PendingTTS
                }
            };
        }

        private List<TextBlockState> CreateSuccessfulTextBlockStates()
        {
            return new List<TextBlockState>
            {
                new TextBlockState
                {
                    TextBlockId = "block1",
                    BookId = "test-book",
                    BlockSequenceNumber = 1,
                    TextFilePath = @"C:\test\prepare\0001.txt",
                    WavFilePath = @"C:\test\wav\0001.wav",
                    Status = TextBlockStatus.TTS_Success
                },
                new TextBlockState
                {
                    TextBlockId = "block2",
                    BookId = "test-book",
                    BlockSequenceNumber = 2,
                    TextFilePath = @"C:\test\prepare\0002.txt",
                    WavFilePath = @"C:\test\wav\0002.wav",
                    Status = TextBlockStatus.TTS_Success
                }
            };
        }
    }
}
