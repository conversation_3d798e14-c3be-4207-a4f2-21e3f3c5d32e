using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.State.Entities;
using System.IO;

namespace ProjectScribe.Logic.State
{
    /// <summary>
    /// Implementation of IStateManager for managing application state using Entity Framework Core and SQLite.
    /// Provides comprehensive state tracking for book processing and text block conversion.
    /// </summary>
    public class StateManager : IStateManager
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<StateManager> _logger;

        /// <summary>
        /// Initializes a new instance of the StateManager.
        /// </summary>
        /// <param name="dbContext">Database context for state operations.</param>
        /// <param name="logger">Logger for diagnostic information.</param>
        public StateManager(AppDbContext dbContext, ILogger<StateManager> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Book State Management

        /// <inheritdoc />
        public async Task InitializeBookAsync(string bookId, string filePath)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be null or empty.", nameof(filePath));

            try
            {
                // Check if book already exists
                var existingBook = await _dbContext.BookProcessStates
                    .FirstOrDefaultAsync(b => b.BookId == bookId);

                if (existingBook != null)
                {
                    _logger.LogInformation("Book {BookId} already exists in database with status {Status}", 
                        bookId, existingBook.Status);
                    return;
                }

                var bookState = new BookProcessState
                {
                    BookId = bookId,
                    FilePath = filePath,
                    Status = BookProcessStatus.Pending,
                    LastProcessedStep = "Book initialized",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _dbContext.BookProcessStates.Add(bookState);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Initialized book {BookId} with file path {FilePath}", bookId, filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize book {BookId} with file path {FilePath}", bookId, filePath);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<BookProcessState?> GetBookStateAsync(string bookId)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                return await _dbContext.BookProcessStates
                    .Include(b => b.TextBlocks)
                    .FirstOrDefaultAsync(b => b.BookId == bookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve book state for {BookId}", bookId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<IEnumerable<BookProcessState>> GetIncompleteBooksAsync()
        {
            try
            {
                return await _dbContext.BookProcessStates
                    .Where(b => b.Status != BookProcessStatus.Completed && b.Status != BookProcessStatus.Failed)
                    .Include(b => b.TextBlocks)
                    .OrderBy(b => b.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve incomplete books");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task UpdateBookStatusAsync(string bookId, BookProcessStatus status, string? lastStep = null, string? errorMessage = null)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                var bookState = await _dbContext.BookProcessStates
                    .FirstOrDefaultAsync(b => b.BookId == bookId);

                if (bookState == null)
                {
                    _logger.LogWarning("Attempted to update non-existent book {BookId}", bookId);
                    throw new InvalidOperationException($"Book {bookId} not found in database.");
                }

                bookState.Status = status;
                bookState.UpdatedAt = DateTime.UtcNow;
                if (!string.IsNullOrWhiteSpace(lastStep))
                    bookState.LastProcessedStep = lastStep;
                if (errorMessage != null)
                    bookState.ErrorMessage = errorMessage;

                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Updated book {BookId} status to {Status}", bookId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update book status for {BookId}", bookId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<bool> BookExistsAsync(string bookId)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                return await _dbContext.BookProcessStates
                    .AnyAsync(b => b.BookId == bookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if book {BookId} exists", bookId);
                throw;
            }
        }

        #endregion

        #region Text Block State Management

        /// <inheritdoc />
        public async Task AddTextBlockAsync(TextBlockState textBlock)
        {
            if (textBlock == null)
                throw new ArgumentNullException(nameof(textBlock));

            try
            {
                _dbContext.TextBlockStates.Add(textBlock);
                await _dbContext.SaveChangesAsync();

                _logger.LogDebug("Added text block {TextBlockId} for book {BookId}", 
                    textBlock.TextBlockId, textBlock.BookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add text block {TextBlockId} for book {BookId}", 
                    textBlock.TextBlockId, textBlock.BookId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task AddTextBlocksAsync(IEnumerable<TextBlockState> textBlocks)
        {
            if (textBlocks == null)
                throw new ArgumentNullException(nameof(textBlocks));

            var blockList = textBlocks.ToList();
            if (!blockList.Any())
                return;

            try
            {
                _dbContext.TextBlockStates.AddRange(blockList);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Added {Count} text blocks for book {BookId}", 
                    blockList.Count, blockList.First().BookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add {Count} text blocks", blockList.Count);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<IEnumerable<TextBlockState>> GetTextBlocksForBookByStatusAsync(string bookId, TextBlockStatus status)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                return await _dbContext.TextBlockStates
                    .Where(tb => tb.BookId == bookId && tb.Status == status)
                    .OrderBy(tb => tb.BlockSequenceNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve text blocks for book {BookId} with status {Status}", 
                    bookId, status);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<IEnumerable<TextBlockState>> GetTextBlocksForBookAsync(string bookId)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                return await _dbContext.TextBlockStates
                    .Where(tb => tb.BookId == bookId)
                    .OrderBy(tb => tb.BlockSequenceNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve text blocks for book {BookId}", bookId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<IEnumerable<TextBlockState>> GetPendingTextBlocksAsync(string bookId)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                // Include PendingTTS, TTS_InProgress, and TTS_Failed blocks as "pending"
                // - PendingTTS: Never attempted
                // - TTS_InProgress: Left over from interrupted processing
                // - TTS_Failed: Failed but can be retried (not permanently failed)
                return await _dbContext.TextBlockStates
                    .Where(tb => tb.BookId == bookId &&
                                (tb.Status == TextBlockStatus.PendingTTS ||
                                 tb.Status == TextBlockStatus.TTS_InProgress ||
                                 tb.Status == TextBlockStatus.TTS_Failed))
                    .OrderBy(tb => tb.BlockSequenceNumber)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve pending text blocks for book {BookId}", bookId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task UpdateTextBlockStatusAsync(string textBlockId, TextBlockStatus status, string? wavPath = null, int? retryCount = null, string? errorMessage = null)
        {
            if (string.IsNullOrWhiteSpace(textBlockId))
                throw new ArgumentException("Text block ID cannot be null or empty.", nameof(textBlockId));

            try
            {
                var textBlock = await _dbContext.TextBlockStates
                    .FirstOrDefaultAsync(tb => tb.TextBlockId == textBlockId);

                if (textBlock == null)
                {
                    _logger.LogWarning("Attempted to update non-existent text block {TextBlockId}", textBlockId);
                    throw new InvalidOperationException($"Text block {textBlockId} not found in database.");
                }

                textBlock.Status = status;
                if (!string.IsNullOrWhiteSpace(wavPath))
                    textBlock.WavFilePath = wavPath;
                if (retryCount.HasValue)
                    textBlock.RetryCount = retryCount.Value;
                if (errorMessage != null)
                    textBlock.ErrorMessage = errorMessage;

                await _dbContext.SaveChangesAsync();

                _logger.LogDebug("Updated text block {TextBlockId} status to {Status}", textBlockId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update text block status for {TextBlockId}", textBlockId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<TextBlockState?> GetTextBlockAsync(string textBlockId)
        {
            if (string.IsNullOrWhiteSpace(textBlockId))
                throw new ArgumentException("Text block ID cannot be null or empty.", nameof(textBlockId));

            try
            {
                return await _dbContext.TextBlockStates
                    .FirstOrDefaultAsync(tb => tb.TextBlockId == textBlockId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve text block {TextBlockId}", textBlockId);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<int> GetTextBlockCountByStatusAsync(string bookId, TextBlockStatus status)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                return await _dbContext.TextBlockStates
                    .CountAsync(tb => tb.BookId == bookId && tb.Status == status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to count text blocks for book {BookId} with status {Status}",
                    bookId, status);
                throw;
            }
        }

        /// <summary>
        /// Resets text blocks that are stuck in TTS_InProgress status back to PendingTTS.
        /// This handles cases where processing was interrupted and blocks were left in an in-progress state.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>Number of blocks that were reset.</returns>
        public async Task<int> ResetInProgressTextBlocksAsync(string bookId)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                var inProgressBlocks = await _dbContext.TextBlockStates
                    .Where(tb => tb.BookId == bookId && tb.Status == TextBlockStatus.TTS_InProgress)
                    .ToListAsync();

                if (inProgressBlocks.Any())
                {
                    foreach (var block in inProgressBlocks)
                    {
                        block.Status = TextBlockStatus.PendingTTS;
                        // Clear any partial error state
                        block.ErrorMessage = null;
                    }

                    await _dbContext.SaveChangesAsync();

                    _logger.LogInformation("Reset {Count} text blocks from TTS_InProgress to PendingTTS for book {BookId}",
                        inProgressBlocks.Count, bookId);
                }

                return inProgressBlocks.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reset in-progress text blocks for book {BookId}", bookId);
                throw;
            }
        }

        /// <summary>
        /// Resets text blocks that are in TTS_FailedPermanent status back to PendingTTS.
        /// This allows permanently failed blocks to be retried on application restart.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>Number of blocks that were reset.</returns>
        public async Task<int> ResetPermanentlyFailedTextBlocksAsync(string bookId)
        {
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            try
            {
                var permanentlyFailedBlocks = await _dbContext.TextBlockStates
                    .Where(tb => tb.BookId == bookId && tb.Status == TextBlockStatus.TTS_FailedPermanent)
                    .ToListAsync();

                if (permanentlyFailedBlocks.Any())
                {
                    foreach (var block in permanentlyFailedBlocks)
                    {
                        block.Status = TextBlockStatus.PendingTTS;
                        // Reset retry count to allow fresh attempts
                        block.RetryCount = 0;
                        // Clear error message
                        block.ErrorMessage = null;
                        // Clear any WAV file path from previous failed attempts
                        block.WavFilePath = null;
                    }

                    await _dbContext.SaveChangesAsync();

                    _logger.LogInformation("Reset {Count} text blocks from TTS_FailedPermanent to PendingTTS for book {BookId}",
                        permanentlyFailedBlocks.Count, bookId);
                }

                return permanentlyFailedBlocks.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reset permanently failed text blocks for book {BookId}", bookId);
                throw;
            }
        }

        #endregion

        #region Database Management

        /// <inheritdoc />
        public async Task InitializeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("Initializing database...");

                // Ensure database directory exists
                EnsureDatabaseDirectoryExists();

                await _dbContext.Database.EnsureCreatedAsync();
                _logger.LogInformation("Database initialization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize database");
                throw;
            }
        }

        /// <summary>
        /// Ensures that the directory for the database file exists.
        /// Extracts the database path from the connection string and creates the directory if needed.
        /// </summary>
        private void EnsureDatabaseDirectoryExists()
        {
            try
            {
                var connectionString = _dbContext.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogWarning("Database connection string is null or empty");
                    return;
                }

                // Extract the database file path from SQLite connection string
                // Format: "Data Source=path/to/database.db" or "Data Source=path\\to\\database.db"
                var dataSourcePrefix = "Data Source=";
                var dataSourceIndex = connectionString.IndexOf(dataSourcePrefix, StringComparison.OrdinalIgnoreCase);
                if (dataSourceIndex == -1)
                {
                    _logger.LogWarning("Could not find 'Data Source=' in connection string: {ConnectionString}", connectionString);
                    return;
                }

                var dbPath = connectionString.Substring(dataSourceIndex + dataSourcePrefix.Length);

                // Remove any trailing semicolon and trim
                var semicolonIndex = dbPath.IndexOf(';');
                if (semicolonIndex != -1)
                {
                    dbPath = dbPath.Substring(0, semicolonIndex);
                }
                dbPath = dbPath.Trim();

                // Get the directory path
                var directoryPath = Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                    _logger.LogInformation("Created database directory: {DirectoryPath}", directoryPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to ensure database directory exists, but continuing with database initialization");
                // Don't throw - let the database creation attempt proceed
            }
        }

        #endregion
    }
}
