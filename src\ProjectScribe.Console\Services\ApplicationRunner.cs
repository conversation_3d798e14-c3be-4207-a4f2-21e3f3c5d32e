using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Monitoring;
using SimpleInjector;

namespace ProjectScribe.Console.Services
{
    /// <summary>
    /// Manages the main application execution including directory monitoring and graceful shutdown.
    /// </summary>
    public class ApplicationRunner
    {
        private readonly Container _container;
        private readonly ILogger<ApplicationRunner> _logger;
        private readonly IDirectoryMonitor _directoryMonitor;
        
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isRunning;

        public ApplicationRunner(
            Container container,
            ILogger<ApplicationRunner> logger,
            IDirectoryMonitor directoryMonitor)
        {
            _container = container;
            _logger = logger;
            _directoryMonitor = directoryMonitor;
        }

        /// <summary>
        /// Runs the main application loop with directory monitoring.
        /// </summary>
        public async Task RunAsync()
        {
            if (_isRunning)
            {
                throw new InvalidOperationException("Application is already running");
            }

            _isRunning = true;
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                await StartDirectoryMonitoring();
                await WaitForShutdownSignal();
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                _logger.LogInformation("Application shutdown initiated");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during application execution");
                throw;
            }
            finally
            {
                await StopDirectoryMonitoring();
                _isRunning = false;
            }
        }

        /// <summary>
        /// Stops the application gracefully.
        /// </summary>
        public async Task StopAsync()
        {
            if (!_isRunning || _cancellationTokenSource == null)
            {
                return;
            }

            _logger.LogInformation("Shutdown requested. Stopping application...");
            _cancellationTokenSource.Cancel();

            // Wait a moment for graceful shutdown
            await Task.Delay(100);
        }

        /// <summary>
        /// Starts directory monitoring and sets up file detection event handling.
        /// </summary>
        private Task StartDirectoryMonitoring()
        {
            _logger.LogInformation("Starting Directory Monitoring service...");

            try
            {
                // Subscribe to the NewFileDetected event
                _directoryMonitor.NewFileDetected += OnNewFileDetected;

                // Start monitoring
                _directoryMonitor.StartMonitoring();
                _logger.LogInformation("Directory monitoring started successfully");

                // Display user instructions
                DisplayUserInstructions();

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during directory monitoring setup");
                throw;
            }
        }

        /// <summary>
        /// Stops directory monitoring and cleans up resources.
        /// </summary>
        private Task StopDirectoryMonitoring()
        {
            try
            {
                if (_directoryMonitor != null)
                {
                    _directoryMonitor.NewFileDetected -= OnNewFileDetected;
                    _directoryMonitor.StopMonitoring();
                    _directoryMonitor.Dispose();
                    _logger.LogInformation("Directory monitoring stopped and disposed");
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while stopping directory monitoring");
                throw;
            }
        }

        /// <summary>
        /// Handles new file detection events from the directory monitor.
        /// </summary>
        private void OnNewFileDetected(object? sender, string filePath)
        {
            _logger.LogInformation("New file detected for processing: {FilePath}", filePath);

            // Process the book asynchronously using safe processing
            _ = Task.Run(async () =>
            {
                await StartupService.SafeProcessBookAsync(_container, _logger, filePath, "NewFile");
            });
        }

        /// <summary>
        /// Sets up graceful shutdown handling and waits for shutdown signal.
        /// </summary>
        private async Task WaitForShutdownSignal()
        {
            // Set up graceful shutdown handling
            System.Console.CancelKeyPress += OnCancelKeyPress;

            try
            {
                // Keep the application running until cancellation is requested
                await Task.Delay(Timeout.Infinite, _cancellationTokenSource!.Token);
            }
            finally
            {
                System.Console.CancelKeyPress -= OnCancelKeyPress;
            }
        }

        /// <summary>
        /// Handles Ctrl+C key press for graceful shutdown.
        /// </summary>
        private void OnCancelKeyPress(object? sender, ConsoleCancelEventArgs e)
        {
            e.Cancel = true; // Prevent immediate termination
            _logger.LogInformation("Shutdown requested. Stopping directory monitoring...");
            _cancellationTokenSource?.Cancel();
        }

        /// <summary>
        /// Displays user instructions in the console.
        /// </summary>
        private static void DisplayUserInstructions()
        {
            System.Console.WriteLine("ProjectScribe is now monitoring for new files.");
            System.Console.WriteLine("Place .epub files in the configured input directory to see them detected.");
            System.Console.WriteLine("The console window has been minimized to the taskbar.");
            System.Console.WriteLine("Click on the ProjectScribe icon in the taskbar to restore this window.");
            System.Console.WriteLine("Press Ctrl+C to stop monitoring and exit.");
        }
    }
}
