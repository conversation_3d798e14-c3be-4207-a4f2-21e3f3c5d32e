using ProjectScribe.Logic.Parsing.Models;
using System.Collections.Generic;

namespace ProjectScribe.Logic.Text
{
    /// <summary>
    /// Interface for text processing services that handle sanitization and splitting of chapter content.
    /// Responsible for converting raw chapter text into manageable blocks suitable for TTS processing.
    /// </summary>
    public interface ITextProcessor
    {
        /// <summary>
        /// Processes a chapter's content by sanitizing the text and splitting it into manageable blocks.
        /// </summary>
        /// <param name="chapterContent">The chapter content to process, containing title and raw text.</param>
        /// <param name="bookId">The unique identifier of the book this chapter belongs to.</param>
        /// <returns>A collection of text blocks ready for TTS processing, ordered by sequence.</returns>
        /// <exception cref="System.ArgumentNullException">Thrown when chapterContent or bookId is null.</exception>
        /// <exception cref="System.ArgumentException">Thrown when bookId is empty or whitespace.</exception>
        IEnumerable<TextBlock> ProcessChapter(ChapterContent chapterContent, string bookId);
    }
}
