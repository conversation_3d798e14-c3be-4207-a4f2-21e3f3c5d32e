You are an expert software architect. I will provide you with detailed project specifications.
Your task is to generate a comprehensive and well-organized directory and file structure tailored to these specifications.

The project specifications I will provide include:
1.  **Core Purpose & Main Functionalities:** A description of what the project aims to achieve and its key features.
2.  **Project Type:** e.g., Web Application, API Service, Mobile App, CLI Tool, Library.
3.  **Technology Stack:** Details on:
    *   Frontend technologies (e.g., React with TypeScript, Next.js, Vue, Angular, Svelte, Vite, Webpack, Tailwind CSS).
    *   Backend technologies (e.g., Node.js with Express/NestJS, Python with Django/FastAPI, Java with Spring Boot, Ruby on Rails, Go, Rust).
    *   Database(s) (e.g., PostgreSQL, MySQL, MongoDB, Cassandra, Redis, Elasticsearch).
    *   State Management solutions (if applicable, e.g., Redux, Zustand, Vuex, Pinia, NgRx).
    *   API Styles (if applicable, e.g., REST, GraphQL, gRPC, WebSockets).
    *   Other key tools, frameworks, or libraries (e.g., Docker, Kubernetes, ORMs like Prisma/SQLAlchemy/Hibernate, Authentication services like Auth0/Keycloak/Firebase Auth, Testing frameworks like Jest/PyTest/JUnit/Cypress, Message Queues like RabbitMQ/Kafka).
4.  **Architectural Pattern(s):** The chosen high-level design (e.g., Monolith, Microservices, Serverless, Layered Architecture, MVC, MVVM, MVP, Feature-Sliced Design, Domain-Driven Design (DDD), Clean Architecture, Hexagonal Architecture).
5.  **Primary Modules/Features:** A list of the main functional components or user-facing features (e.g., User Authentication & Authorization, Product Catalog Management, Order Processing, Payment Gateway Integration, Notification System, Admin Dashboard, Reporting).
6.  **Code Organization Preferences:** Specific choices regarding:
    *   Repository Structure (e.g., Monorepo using Nx/Turborepo/Lerna, Polyrepo).
    *   Source Code Grouping Strategy (e.g., Group by feature/module, Group by technical type like controllers/services/models).
    *   Required Top-Level Directories (e.g., `docs/`, `tests/`, `scripts/`, `config/`, `infra/`, `data/`).
    *   Test Organization Strategy (e.g., Tests co-located with source files, Central `tests/` directory, Separate test directories per module/feature, Grouped by test type like unit/integration/e2e).
7.  **Team Context (Optional):** Information about team size, distribution, or experience, if relevant to structural decisions.

Based on the specific details I provide for these points, propose a directory and file structure that adheres to the following requirements:

A.  **Presentation Format:**
    *   The primary output must be a clear, hierarchical text-based tree structure.
    *   Additionally, if possible, provide the same structure in Mermaid.js graph syntax (e.g., using `graph TD` for a top-down hierarchy) for visualization.

B.  **Content and Granularity:**
    *   Include common and relevant top-level project files, tailored to the specified technology stack (e.g., `README.md`, `.gitignore`, language-specific project files like `package.json`, `pyproject.toml`, `pom.xml`, `go.mod`, build tool configurations like `webpack.config.js`, `vite.config.ts`, `Dockerfile`, `docker-compose.yml`, environment configuration templates like `.env.example`, CI/CD pipeline stubs like `.github/workflows/main.yml` or `gitlab-ci.yml`).
    *   Detail typical subdirectories for source code (e.g., `src/`, `app/`, `cmd/`, `pkg/`, `lib/`), assets (`public/`, `static/`, `assets/`), UI components/views/pages, services/business logic/use cases, API handlers/controllers/routes/resolvers, data models/entities/schemas/DTOs, database interactions (migrations, seeds, ORM configurations, repositories/DAOs), utility functions/helpers, middleware, configuration files/modules, internationalization (i18n) resources, and comprehensive testing structures (reflecting the specified organization preference and covering unit, integration, and e2e tests where appropriate).
    *   Suggest idiomatic and representative placeholder names for key files within important directories, following common naming conventions for the specified technologies (e.g., `UserController.ts`, `ProductModel.java`, `auth_service.py`, `order.routes.js`, `Button.tsx`, `CreateUserUseCase.ts`, `user.repository.ts`, `schema.prisma`, `V1__create_users_table.sql`).

C.  **Rationale and Best Practices:**
    *   Provide brief, clear explanations for the purpose of major top-level directories and crucial subdirectories within the source code. Justify these choices in relation to the provided project context, technology stack, and architectural patterns.
    *   The proposed structure must follow established conventions and best practices for the chosen technologies and architectural patterns, emphasizing scalability, maintainability, testability, and clarity.

D.  **Specific Organizational Aspects:**
    *   If the project involves multiple distinct parts (e.g., frontend, backend, shared libraries within a monolith or monorepo), clearly distinguish their organization using conventional directory names (e.g., `apps/client/`, `apps/server/`, `packages/shared/`, `services/service-name/` for microservices).
    *   Illustrate how shared code, reusable libraries, or common modules would be organized, particularly in monorepo setups or systems with multiple interacting services.

E.  **Consideration of Alternatives:**
    *   If multiple widely accepted conventions or structural approaches are viable for certain parts of the project (given the provided context), recommend one option and briefly discuss notable alternatives, outlining their respective advantages and disadvantages or trade-offs.

The ultimate goal is to receive a practical, robust, and well-reasoned directory and file structure that serves as an excellent starting point for the project's codebase, meticulously tailored to the specific requirements and context I provide.