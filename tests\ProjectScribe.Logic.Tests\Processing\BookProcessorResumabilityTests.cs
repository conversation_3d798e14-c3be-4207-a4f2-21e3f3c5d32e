using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Audio;
using ProjectScribe.Logic.Common;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Parsing;
using ProjectScribe.Logic.Parsing.Models;
using ProjectScribe.Logic.Processing;
using ProjectScribe.Logic.State;
using ProjectScribe.Logic.State.Entities;
using ProjectScribe.Logic.Text;
using ProjectScribe.Logic.TTS;
using System.IO;
using Xunit;

namespace ProjectScribe.Logic.Tests.Processing
{
    /// <summary>
    /// Tests for BookProcessor resumability functionality.
    /// Verifies that the processor can correctly resume from various states.
    /// </summary>
    public class BookProcessorResumabilityTests
    {
        private readonly Mock<IConfigurationManager> _mockConfigurationManager;
        private readonly Mock<ILogger<BookProcessor>> _mockLogger;
        private readonly Mock<IStateManager> _mockStateManager;
        private readonly Mock<IInputParser> _mockInputParser;
        private readonly Mock<ITextProcessor> _mockTextProcessor;
        private readonly Mock<IStyleProcessor> _mockStyleProcessor;
        private readonly Mock<ITtsClient> _mockTtsClient;
        private readonly Mock<IAudioPostProcessor> _mockAudioPostProcessor;
        private readonly BookProcessor _bookProcessor;
        private readonly AppSettings _appSettings;

        public BookProcessorResumabilityTests()
        {
            _mockConfigurationManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<BookProcessor>>();
            _mockStateManager = new Mock<IStateManager>();
            _mockInputParser = new Mock<IInputParser>();
            _mockTextProcessor = new Mock<ITextProcessor>();
            _mockStyleProcessor = new Mock<IStyleProcessor>();
            _mockTtsClient = new Mock<ITtsClient>();
            _mockAudioPostProcessor = new Mock<IAudioPostProcessor>();

            _appSettings = new AppSettings
            {
                BaseLibraryDirectory = Path.GetTempPath(),
                PrepareDirectoryName = "prepare",
                WavDirectoryName = "wav",
                OutputDirectoryName = "output",
                TextBlockTargetCharCount = 100,
                MaxConcurrentTtsThreads = 1,
                MaxTtsRetries = 3,
                TtsRetryInitialDelayMs = 100,
                TtsRetryBackoffMultiplier = 1.5f,
                FinalAudioFormat = "opus",
                FinalAudioBitrate = "128k",
                MaxAudioBatchDurationMinutes = 60
            };

            _mockConfigurationManager.Setup(x => x.GetAppSettings()).Returns(_appSettings);

            _bookProcessor = new BookProcessor(
                _mockConfigurationManager.Object,
                _mockLogger.Object,
                _mockStateManager.Object,
                _mockInputParser.Object,
                _mockTextProcessor.Object,
                _mockStyleProcessor.Object,
                _mockTtsClient.Object,
                _mockAudioPostProcessor.Object
            );
        }

        [Fact]
        public async Task ProcessBookAsync_NewBook_ProcessesFromBeginning()
        {
            // Arrange
            var testFilePath = CreateTestEpubFile();
            var bookId = FileSystemUtils.GenerateBookId(testFilePath);

            _mockStateManager.Setup(x => x.GetBookStateAsync(bookId))
                .ReturnsAsync((BookProcessState?)null);

            _mockInputParser.Setup(x => x.ParseAsync(testFilePath))
                .ReturnsAsync(new List<ChapterContent>
                {
                    new ChapterContent { Title = "Chapter 1", TextContent = "Test content" }
                });

            _mockTextProcessor.Setup(x => x.ProcessChapter(It.IsAny<ChapterContent>(), bookId))
                .Returns(new List<TextBlock>
                {
                    new TextBlock { BlockId = "block-1", SanitizedText = "Test content" }
                });

            _mockStateManager.Setup(x => x.GetPendingTextBlocksAsync(bookId))
                .ReturnsAsync(new List<TextBlockState>());

            _mockStateManager.Setup(x => x.GetTextBlocksForBookByStatusAsync(bookId, TextBlockStatus.TTS_Success))
                .ReturnsAsync(new List<TextBlockState>());

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert
            _mockStateManager.Verify(x => x.InitializeBookAsync(bookId, testFilePath), Times.Once);
            _mockInputParser.Verify(x => x.ParseAsync(testFilePath), Times.Once);
            _mockStateManager.Verify(x => x.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingText, "Parsing", null), Times.Once);

            // Cleanup
            File.Delete(testFilePath);
        }

        [Fact]
        public async Task ProcessBookAsync_CompletedBook_SkipsProcessing()
        {
            // Arrange
            var testFilePath = CreateTestEpubFile();
            var bookId = FileSystemUtils.GenerateBookId(testFilePath);

            var completedBookState = new BookProcessState
            {
                BookId = bookId,
                FilePath = testFilePath,
                Status = BookProcessStatus.Completed,
                LastProcessedStep = "AudioPostProcessing_Complete"
            };

            _mockStateManager.Setup(x => x.GetBookStateAsync(bookId))
                .ReturnsAsync(completedBookState);

            // Act
            await _bookProcessor.ProcessBookAsync(testFilePath);

            // Assert
            _mockInputParser.Verify(x => x.ParseAsync(It.IsAny<string>()), Times.Never);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("already completed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.AtLeastOnce);

            // Cleanup
            File.Delete(testFilePath);
        }

        private string CreateTestEpubFile()
        {
            var tempFile = Path.GetTempFileName();
            File.WriteAllText(tempFile, "Test epub content");
            return tempFile;
        }
    }
}
