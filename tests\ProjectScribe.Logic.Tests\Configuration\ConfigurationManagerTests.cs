using Microsoft.Extensions.Configuration;
using ProjectScribe.Logic.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using Xunit;

namespace ProjectScribe.Logic.Tests.Configuration
{
    /// <summary>
    /// Unit tests for the ConfigurationManager class.
    /// Tests configuration loading, default values, and error handling.
    /// </summary>
    public class ConfigurationManagerTests : IDisposable
    {
        private readonly string _baseDirectory;
        private readonly string _backupConfigPath;
        private readonly bool _originalConfigExisted;

        public ConfigurationManagerTests()
        {
            // Use the actual AppContext.BaseDirectory where ConfigurationManager looks
            _baseDirectory = AppContext.BaseDirectory;
            _backupConfigPath = Path.Combine(_baseDirectory, "config.json.backup");

            // Backup existing config.json if it exists
            var configPath = Path.Combine(_baseDirectory, "config.json");
            _originalConfigExisted = File.Exists(configPath);
            if (_originalConfigExisted)
            {
                File.Move(configPath, _backupConfigPath);
            }
        }

        public void Dispose()
        {
            // Clean up test config file
            var configPath = Path.Combine(_baseDirectory, "config.json");
            if (File.Exists(configPath))
            {
                File.Delete(configPath);
            }

            // Restore original config.json if it existed
            if (_originalConfigExisted && File.Exists(_backupConfigPath))
            {
                File.Move(_backupConfigPath, configPath);
            }
        }

        [Fact]
        public void ConfigurationManager_ShouldUseDefaults_WhenConfigFileDoesNotExist()
        {
            // Arrange - No config.json file exists in test directory

            // Act
            var configManager = new ProjectScribe.Logic.Configuration.ConfigurationManager();
            var settings = configManager.GetAppSettings();

            // Assert - Verify all default values
            Assert.Equal("d:\\lib", settings.BaseLibraryDirectory);
            Assert.Equal("input", settings.InputDirectoryName);
            Assert.Equal("prepare", settings.PrepareDirectoryName);
            Assert.Equal("wav", settings.WavDirectoryName);
            Assert.Equal("output", settings.OutputDirectoryName);
            Assert.NotNull(settings.SupportedInputFileExtensions);
            Assert.Single(settings.SupportedInputFileExtensions);
            Assert.Contains(".epub", settings.SupportedInputFileExtensions);
            Assert.Equal("https://gemini-openai-adapter.100169.xyz/tts", settings.GeminiTtsApiEndpoint);
            Assert.Equal("Charon", settings.GeminiTtsFirstVoiceName);
            Assert.Equal("Zephyr", settings.GeminiTtsSecondVoiceName);
            Assert.Equal("gemini-2.5-flash-preview-tts", settings.GeminiTtsModel);
            Assert.Equal(1800, settings.TextBlockTargetCharCount);
            Assert.Equal(4, settings.MaxConcurrentTtsThreads);
            Assert.Equal(3, settings.MaxTtsRetries);
            Assert.Equal(30000, settings.TtsRetryInitialDelayMs);
            Assert.Equal(2.0f, settings.TtsRetryBackoffMultiplier);
            Assert.Equal("opus", settings.FinalAudioFormat);
            Assert.Equal("128k", settings.FinalAudioBitrate);
            Assert.Equal(60, settings.MaxAudioBatchDurationMinutes);
            Assert.Null(settings.FfmpegPath);
            Assert.Equal("Information", settings.DefaultLogLevel);
            Assert.Equal("data/project_scribe.db", settings.DatabasePath);
        }

        [Fact]
        public void ConfigurationManager_ShouldLoadFromConfigFile_WhenConfigFileExists()
        {
            // Arrange
            var testConfig = new
            {
                BaseLibraryDirectory = "C:\\TestLib",
                InputDirectoryName = "test_input",
                PrepareDirectoryName = "test_prepare",
                WavDirectoryName = "test_wav",
                OutputDirectoryName = "test_output",
                SupportedInputFileExtensions = new[] { ".epub", ".pdf", ".txt" },
                GeminiTtsApiEndpoint = "https://test-api.example.com/tts",
                GeminiTtsFirstVoiceName = "TestVoice1",
                GeminiTtsSecondVoiceName = "TestVoice2",
                GeminiTtsModel = "test-model",
                TextBlockTargetCharCount = 2500,
                MaxConcurrentTtsThreads = 8,
                MaxTtsRetries = 5,
                TtsRetryInitialDelayMs = 3000,
                TtsRetryBackoffMultiplier = 1.5f,
                FinalAudioFormat = "mp3",
                FinalAudioBitrate = "128k",
                MaxAudioBatchDurationMinutes = 90,
                FfmpegPath = "C:\\ffmpeg\\bin\\ffmpeg.exe",
                DefaultLogLevel = "Debug",
                DatabasePath = "test_data/test.db"
            };

            var configJson = JsonSerializer.Serialize(testConfig, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(Path.Combine(_baseDirectory, "config.json"), configJson);

            // Act
            var configManager = new ProjectScribe.Logic.Configuration.ConfigurationManager();
            var settings = configManager.GetAppSettings();

            // Assert
            Assert.Equal("C:\\TestLib", settings.BaseLibraryDirectory);
            Assert.Equal("test_input", settings.InputDirectoryName);
            Assert.Equal("test_prepare", settings.PrepareDirectoryName);
            Assert.Equal("test_wav", settings.WavDirectoryName);
            Assert.Equal("test_output", settings.OutputDirectoryName);
            Assert.Equal(4, settings.SupportedInputFileExtensions.Count);
            Assert.Contains(".epub", settings.SupportedInputFileExtensions);
            Assert.Contains(".pdf", settings.SupportedInputFileExtensions);
            Assert.Contains(".txt", settings.SupportedInputFileExtensions);
            Assert.Equal("https://test-api.example.com/tts", settings.GeminiTtsApiEndpoint);
            Assert.Equal("TestVoice1", settings.GeminiTtsFirstVoiceName);
            Assert.Equal("TestVoice2", settings.GeminiTtsSecondVoiceName);
            Assert.Equal("test-model", settings.GeminiTtsModel);
            Assert.Equal(2500, settings.TextBlockTargetCharCount);
            Assert.Equal(8, settings.MaxConcurrentTtsThreads);
            Assert.Equal(5, settings.MaxTtsRetries);
            Assert.Equal(3000, settings.TtsRetryInitialDelayMs);
            Assert.Equal(1.5f, settings.TtsRetryBackoffMultiplier);
            Assert.Equal("mp3", settings.FinalAudioFormat);
            Assert.Equal("128k", settings.FinalAudioBitrate);
            Assert.Equal(90, settings.MaxAudioBatchDurationMinutes);
            Assert.Equal("C:\\ffmpeg\\bin\\ffmpeg.exe", settings.FfmpegPath);
            Assert.Equal("Debug", settings.DefaultLogLevel);
            Assert.Equal("test_data/test.db", settings.DatabasePath);
        }

        [Fact]
        public void ConfigurationManager_ShouldUseDefaultsForMissingValues_WhenConfigFileIsPartial()
        {
            // Arrange - Create config with only some values
            var partialConfig = new
            {
                BaseLibraryDirectory = "C:\\PartialLib",
                TextBlockTargetCharCount = 3000,
                DefaultLogLevel = "Warning"
                // Other values missing - should use defaults
            };

            var configJson = JsonSerializer.Serialize(partialConfig, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(Path.Combine(_baseDirectory, "config.json"), configJson);

            // Act
            var configManager = new ProjectScribe.Logic.Configuration.ConfigurationManager();
            var settings = configManager.GetAppSettings();

            // Assert - Overridden values
            Assert.Equal("C:\\PartialLib", settings.BaseLibraryDirectory);
            Assert.Equal(3000, settings.TextBlockTargetCharCount);
            Assert.Equal("Warning", settings.DefaultLogLevel);

            // Assert - Default values for missing properties
            Assert.Equal("input", settings.InputDirectoryName);
            Assert.Equal("prepare", settings.PrepareDirectoryName);
            Assert.Equal("wav", settings.WavDirectoryName);
            Assert.Equal("output", settings.OutputDirectoryName);
            Assert.NotNull(settings.SupportedInputFileExtensions);
            Assert.Single(settings.SupportedInputFileExtensions);
            Assert.Contains(".epub", settings.SupportedInputFileExtensions);
            Assert.Equal("https://gemini-openai-adapter.100169.xyz/tts", settings.GeminiTtsApiEndpoint);
            Assert.Equal("Charon", settings.GeminiTtsFirstVoiceName);
            Assert.Equal("Zephyr", settings.GeminiTtsSecondVoiceName);
            Assert.Equal("gemini-2.5-flash-preview-tts", settings.GeminiTtsModel);
            Assert.Equal(4, settings.MaxConcurrentTtsThreads);
            Assert.Equal(3, settings.MaxTtsRetries);
            Assert.Equal(30000, settings.TtsRetryInitialDelayMs);
            Assert.Equal(2.0f, settings.TtsRetryBackoffMultiplier);
            Assert.Equal("opus", settings.FinalAudioFormat);
            Assert.Equal("128k", settings.FinalAudioBitrate);
            Assert.Equal(60, settings.MaxAudioBatchDurationMinutes);
            Assert.Null(settings.FfmpegPath);
            Assert.Equal("data/project_scribe.db", settings.DatabasePath);
        }

        [Fact]
        public void ConfigurationManager_ShouldHandleInvalidJson_GracefullyUsingDefaults()
        {
            // Arrange - Create invalid JSON file
            var invalidJson = "{ invalid json content }";
            File.WriteAllText(Path.Combine(_baseDirectory, "config.json"), invalidJson);

            // Act & Assert - Should not throw exception and use defaults
            var configManager = new ProjectScribe.Logic.Configuration.ConfigurationManager();
            var settings = configManager.GetAppSettings();

            // Should use all default values since JSON is invalid
            Assert.Equal("d:\\lib", settings.BaseLibraryDirectory);
            Assert.Equal("input", settings.InputDirectoryName);
            Assert.Equal("Information", settings.DefaultLogLevel);
        }

        [Fact]
        public void ConfigurationManager_ShouldHandleEmptyConfigFile_UsingDefaults()
        {
            // Arrange - Create empty config file
            File.WriteAllText(Path.Combine(_baseDirectory, "config.json"), "{}");

            // Act
            var configManager = new ProjectScribe.Logic.Configuration.ConfigurationManager();
            var settings = configManager.GetAppSettings();

            // Assert - Should use all default values
            Assert.Equal("d:\\lib", settings.BaseLibraryDirectory);
            Assert.Equal("input", settings.InputDirectoryName);
            Assert.Equal("prepare", settings.PrepareDirectoryName);
            Assert.Equal("Information", settings.DefaultLogLevel);
            Assert.Equal("data/project_scribe.db", settings.DatabasePath);
        }

        [Fact]
        public void GetAppSettings_ShouldReturnSameInstance_OnMultipleCalls()
        {
            // Arrange
            var configManager = new ProjectScribe.Logic.Configuration.ConfigurationManager();

            // Act
            var settings1 = configManager.GetAppSettings();
            var settings2 = configManager.GetAppSettings();

            // Assert
            Assert.Same(settings1, settings2);
        }
    }
}
