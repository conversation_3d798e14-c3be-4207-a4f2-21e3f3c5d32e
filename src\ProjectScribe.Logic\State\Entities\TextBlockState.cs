using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjectScribe.Logic.State.Entities
{
    /// <summary>
    /// Represents the processing state of an individual text block within a book.
    /// Tracks TTS conversion progress and file locations for each text segment.
    /// </summary>
    public class TextBlockState
    {
        /// <summary>
        /// Unique identifier for the text block, typically a GUID.
        /// </summary>
        [Key]
        public string TextBlockId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Foreign key reference to the parent book.
        /// </summary>
        [Required]
        public string BookId { get; set; } = string.Empty;

        /// <summary>
        /// Sequential number of this block within the book (1-based).
        /// Used to maintain proper ordering for audio concatenation.
        /// </summary>
        public int BlockSequenceNumber { get; set; }

        /// <summary>
        /// File path to the text file containing the sanitized text for this block.
        /// </summary>
        [Required]
        public string TextFilePath { get; set; } = string.Empty;

        /// <summary>
        /// File path to the style-enhanced text file for this block.
        /// Null if style enhancement has not completed successfully.
        /// </summary>
        public string? StyledTextFilePath { get; set; }

        /// <summary>
        /// File path to the generated WAV audio file for this block.
        /// Null if TTS processing has not completed successfully.
        /// </summary>
        public string? WavFilePath { get; set; }

        /// <summary>
        /// Current processing status of this text block.
        /// </summary>
        [Required]
        public TextBlockStatus Status { get; set; } = TextBlockStatus.PendingStyleEnhancement;

        /// <summary>
        /// Number of TTS retry attempts made for this block.
        /// Used to implement retry limits and backoff strategies.
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// Error message if TTS processing failed for this block.
        /// Null if no errors occurred or processing is still in progress.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Timestamp when the text block was first created.
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when the text block state was last updated.
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property to the parent book.
        /// </summary>
        [ForeignKey(nameof(BookId))]
        public virtual BookProcessState? Book { get; set; }
    }

    /// <summary>
    /// Enumeration of possible text block processing statuses.
    /// </summary>
    public enum TextBlockStatus
    {
        /// <summary>
        /// Text block is ready for style enhancement processing.
        /// </summary>
        PendingStyleEnhancement = 0,

        /// <summary>
        /// Text block is ready for TTS processing but has not started.
        /// </summary>
        PendingTTS = 1,

        /// <summary>
        /// TTS request is currently in progress.
        /// </summary>
        TTS_InProgress = 2,

        /// <summary>
        /// TTS processing completed successfully and WAV file is available.
        /// </summary>
        TTS_Success = 3,

        /// <summary>
        /// TTS processing failed but may be retried.
        /// </summary>
        TTS_Failed = 4,

        /// <summary>
        /// TTS processing failed permanently after all retry attempts.
        /// </summary>
        TTS_FailedPermanent = 5
    }
}
