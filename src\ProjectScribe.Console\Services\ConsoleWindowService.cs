using Microsoft.Extensions.Logging;
using System.Runtime.InteropServices;

namespace ProjectScribe.Console.Services
{
    /// <summary>
    /// Service for managing console window state, including minimizing to taskbar.
    /// </summary>
    public class ConsoleWindowService
    {
        private readonly ILogger<ConsoleWindowService> _logger;

        // Windows API constants
        private const int SW_HIDE = 0;
        private const int SW_MINIMIZE = 6;
        private const int SW_RESTORE = 9;

        // Windows API imports
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetConsoleWindow();

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool IsIconic(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool SetWindowText(IntPtr hWnd, string lpString);

        /// <summary>
        /// Initializes a new instance of the ConsoleWindowService.
        /// </summary>
        /// <param name="logger">Logger for recording console window operations.</param>
        public ConsoleWindowService(ILogger<ConsoleWindowService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Minimizes the console window to the taskbar.
        /// </summary>
        /// <returns>True if the window was successfully minimized, false otherwise.</returns>
        public bool MinimizeToTaskbar()
        {
            try
            {
                IntPtr consoleWindow = GetConsoleWindow();
                if (consoleWindow == IntPtr.Zero)
                {
                    _logger.LogWarning("Could not get console window handle. Window minimization not available.");
                    return false;
                }

                bool result = ShowWindow(consoleWindow, SW_MINIMIZE);
                if (result)
                {
                    _logger.LogInformation("Console window minimized to taskbar successfully");
                }
                else
                {
                    _logger.LogWarning("Failed to minimize console window to taskbar");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while trying to minimize console window");
                return false;
            }
        }

        /// <summary>
        /// Restores the console window from minimized state.
        /// </summary>
        /// <returns>True if the window was successfully restored, false otherwise.</returns>
        public bool RestoreWindow()
        {
            try
            {
                IntPtr consoleWindow = GetConsoleWindow();
                if (consoleWindow == IntPtr.Zero)
                {
                    _logger.LogWarning("Could not get console window handle. Window restoration not available.");
                    return false;
                }

                bool result = ShowWindow(consoleWindow, SW_RESTORE);
                if (result)
                {
                    _logger.LogInformation("Console window restored successfully");
                }
                else
                {
                    _logger.LogWarning("Failed to restore console window");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while trying to restore console window");
                return false;
            }
        }

        /// <summary>
        /// Checks if the console window is currently minimized.
        /// </summary>
        /// <returns>True if the window is minimized, false otherwise.</returns>
        public bool IsMinimized()
        {
            try
            {
                IntPtr consoleWindow = GetConsoleWindow();
                if (consoleWindow == IntPtr.Zero)
                {
                    return false;
                }

                return IsIconic(consoleWindow);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking if console window is minimized");
                return false;
            }
        }

        /// <summary>
        /// Sets the console window title.
        /// </summary>
        /// <param name="title">The new title for the console window.</param>
        /// <returns>True if the title was set successfully, false otherwise.</returns>
        public bool SetWindowTitle(string title)
        {
            try
            {
                IntPtr consoleWindow = GetConsoleWindow();
                if (consoleWindow == IntPtr.Zero)
                {
                    _logger.LogWarning("Could not get console window handle. Window title setting not available.");
                    return false;
                }

                bool result = SetWindowText(consoleWindow, title);
                if (result)
                {
                    _logger.LogDebug("Console window title set to: {Title}", title);
                }
                else
                {
                    _logger.LogWarning("Failed to set console window title");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while trying to set console window title");
                return false;
            }
        }

        /// <summary>
        /// Gets a value indicating whether console window operations are supported on this platform.
        /// </summary>
        public bool IsSupported => RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
    }
}
