using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using DotNetEnv;

namespace ProjectScribe.Logic.TTS
{
    /// <summary>
    /// Implementation of ITtsClient for the Gemini TTS API.
    /// Handles text-to-speech conversion with retry logic and error handling.
    /// </summary>
    public class GeminiTtsClient : ITtsClient
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly ILogger<GeminiTtsClient> _logger;
        private readonly HttpClient _httpClient;
        private readonly AppSettings _appSettings;
        private readonly string? _apiKey;

        /// <summary>
        /// Initializes a new instance of the GeminiTtsClient.
        /// </summary>
        /// <param name="configurationManager">Configuration manager for app settings.</param>
        /// <param name="logger">Logger for diagnostic information.</param>
        /// <param name="httpClient">HTTP client for API requests.</param>
        public GeminiTtsClient(
            IConfigurationManager configurationManager,
            ILogger<GeminiTtsClient> logger,
            HttpClient httpClient)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            
            _appSettings = _configurationManager.GetAppSettings();
            _apiKey = LoadApiKeyFromEnv();
            
            if (string.IsNullOrEmpty(_apiKey))
            {
                _logger.LogWarning("GEMINI_API_KEY not found in .env file. TTS requests will fail.");
            }
        }

        /// <summary>
        /// Converts the specified text to speech and returns the audio data as a stream.
        /// </summary>
        /// <param name="textToSynthesize">The text to convert to speech.</param>
        /// <returns>A stream containing WAV audio data if successful, or null if conversion fails after all retries.</returns>
        public async Task<Stream?> GetTtsAudioAsync(string textToSynthesize)
        {
            if (string.IsNullOrWhiteSpace(textToSynthesize))
            {
                _logger.LogWarning("Empty or null text provided for TTS conversion.");
                return null;
            }

            if (string.IsNullOrEmpty(_apiKey))
            {
                _logger.LogError("API key is not available. Cannot perform TTS request.");
                return null;
            }

            _logger.LogTrace("Starting TTS conversion for {TextLength} characters", textToSynthesize.Length);

            var payload = new TtsRequestPayload
            {
                Text = textToSynthesize,
                Model = _appSettings.GeminiTtsModel
            };

            var overallStartTime = DateTime.UtcNow;

            for (int attempt = 1; attempt <= _appSettings.MaxTtsRetries; attempt++)
            {
                var attemptStartTime = DateTime.UtcNow;
                try
                {
                    _logger.LogDebug("TTS attempt {Attempt}/{MaxRetries} for text: {TextPreview}...",
                        attempt, _appSettings.MaxTtsRetries,
                        textToSynthesize.Length > 50 ? textToSynthesize[..50] + "..." : textToSynthesize);

                    var audioStream = await SendTtsRequestAsync(payload);
                    var attemptDuration = DateTime.UtcNow - attemptStartTime;

                    if (audioStream != null && await ValidateWavStreamAsync(audioStream))
                    {
                        var totalDuration = DateTime.UtcNow - overallStartTime;
                        _logger.LogInformation("TTS conversion successful on attempt {Attempt} for {TextLength} characters (attempt: {AttemptMs}ms, total: {TotalMs}ms).",
                            attempt, textToSynthesize.Length, attemptDuration.TotalMilliseconds, totalDuration.TotalMilliseconds);
                        return audioStream;
                    }

                    audioStream?.Dispose();
                    _logger.LogWarning("TTS attempt {Attempt} failed validation (duration: {AttemptMs}ms).",
                        attempt, attemptDuration.TotalMilliseconds);
                }
                catch (Exception ex)
                {
                    var attemptDuration = DateTime.UtcNow - attemptStartTime;
                    _logger.LogWarning(ex, "TTS attempt {Attempt} failed with exception after {AttemptMs}ms: {Message}",
                        attempt, attemptDuration.TotalMilliseconds, ex.Message);
                }

                // Apply backoff delay if not the last attempt
                if (attempt < _appSettings.MaxTtsRetries)
                {
                    var delay = CalculateBackoffDelay(attempt);
                    var delaySeconds = delay / 1000.0;
                    var delayDisplay = delaySeconds >= 60
                        ? $"{delaySeconds / 60:F1} minutes"
                        : $"{delaySeconds:F0} seconds";

                    _logger.LogDebug("Waiting {DelayDisplay} ({DelayMs}ms) before retry attempt {NextAttempt}.",
                        delayDisplay, delay, attempt + 1);
                    await Task.Delay(delay);
                }
            }

            var totalFailureDuration = DateTime.UtcNow - overallStartTime;
            _logger.LogError("TTS conversion failed after {MaxRetries} attempts (total: {TotalMs}ms) for text: {TextPreview}...",
                _appSettings.MaxTtsRetries, totalFailureDuration.TotalMilliseconds,
                textToSynthesize.Length > 50 ? textToSynthesize[..50] + "..." : textToSynthesize);
            return null;
        }

        /// <summary>
        /// Sends a TTS request to the Gemini API.
        /// </summary>
        /// <param name="payload">The request payload containing text and model information.</param>
        /// <returns>A stream containing the audio data if successful, or null if failed.</returns>
        private async Task<Stream?> SendTtsRequestAsync(TtsRequestPayload payload)
        {
            var requestUri = $"{_appSettings.GeminiTtsApiEndpoint}?voiceName={_appSettings.GeminiTtsFirstVoiceName}&secondVoiceName={_appSettings.GeminiTtsSecondVoiceName}";
            var jsonContent = JsonSerializer.Serialize(payload);

            _logger.LogTrace("Preparing TTS request with payload size: {PayloadSize} bytes",
                Encoding.UTF8.GetByteCount(jsonContent));

            using var request = new HttpRequestMessage(HttpMethod.Post, requestUri);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
            request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            _logger.LogDebug("Sending TTS request to {RequestUri} with model {Model}, first voice {FirstVoice}, and second voice {SecondVoice}.",
                requestUri, payload.Model, _appSettings.GeminiTtsFirstVoiceName, _appSettings.GeminiTtsSecondVoiceName);

            var requestStartTime = DateTime.UtcNow;
            var response = await _httpClient.SendAsync(request);
            var requestDuration = DateTime.UtcNow - requestStartTime;

            if (response.IsSuccessStatusCode)
            {
                var contentType = response.Content.Headers.ContentType?.MediaType;
                var contentLength = response.Content.Headers.ContentLength ?? 0;

                _logger.LogTrace("TTS API responded successfully in {RequestMs}ms with content type: {ContentType}, length: {ContentLength} bytes",
                    requestDuration.TotalMilliseconds, contentType, contentLength);

                // New API returns base64-encoded PCM data as text
                var base64Audio = await response.Content.ReadAsStringAsync();

                // Extract sample rate from response headers
                string? sampleRateHeader = null;
                if (response.Headers.TryGetValues("X-Sample-Rate", out var sampleRateValues))
                {
                    sampleRateHeader = sampleRateValues.FirstOrDefault();
                }

                if (!int.TryParse(sampleRateHeader, out var sampleRate))
                {
                    _logger.LogWarning("Missing or invalid X-Sample-Rate header: {SampleRateHeader}", sampleRateHeader);
                    return null;
                }

                _logger.LogDebug("Received base64 PCM audio response with {ContentLength} bytes, sample rate: {SampleRate}Hz in {RequestMs}ms.",
                    contentLength, sampleRate, requestDuration.TotalMilliseconds);

                // Convert base64 PCM to WAV format
                var wavStream = await ConvertBase64PcmToWavAsync(base64Audio, sampleRate);
                return wavStream;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("TTS API request failed after {RequestMs}ms with status {StatusCode}: {ErrorContent}",
                    requestDuration.TotalMilliseconds, response.StatusCode, errorContent);
                return null;
            }
        }

        /// <summary>
        /// Converts base64-encoded PCM audio data to WAV format.
        /// </summary>
        /// <param name="base64Audio">Base64-encoded PCM audio data.</param>
        /// <param name="sampleRate">Sample rate of the audio (e.g., 24000).</param>
        /// <returns>A memory stream containing WAV audio data, or null if conversion fails.</returns>
        private async Task<Stream?> ConvertBase64PcmToWavAsync(string base64Audio, int sampleRate)
        {
            try
            {
                _logger.LogTrace("Starting base64 PCM to WAV conversion with sample rate: {SampleRate}Hz", sampleRate);

                // Step 1: Decode base64 to raw PCM bytes
                byte[] pcmData;
                try
                {
                    pcmData = Convert.FromBase64String(base64Audio);
                    _logger.LogTrace("Successfully decoded base64 to {PcmLength} PCM bytes", pcmData.Length);
                }
                catch (FormatException ex)
                {
                    _logger.LogError(ex, "Failed to decode base64 audio data: {Message}", ex.Message);
                    return null;
                }

                // Step 2: Generate 44-byte WAV header
                var wavHeader = GenerateWavHeader(pcmData.Length, sampleRate);
                _logger.LogTrace("Generated WAV header for {PcmLength} bytes of PCM data", pcmData.Length);

                // Step 3: Combine WAV header + PCM data into memory stream
                var wavStream = new MemoryStream(wavHeader.Length + pcmData.Length);
                await wavStream.WriteAsync(wavHeader, 0, wavHeader.Length);
                await wavStream.WriteAsync(pcmData, 0, pcmData.Length);

                // Reset stream position for consumer
                wavStream.Position = 0;

                _logger.LogDebug("Successfully converted base64 PCM to WAV format. Total size: {WavSize} bytes (header: {HeaderSize}, data: {DataSize})",
                    wavStream.Length, wavHeader.Length, pcmData.Length);

                return wavStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting base64 PCM to WAV: {Message}", ex.Message);
                return null;
            }
        }

        /// <summary>
        /// Generates a 44-byte WAV header for 16-bit mono PCM audio.
        /// </summary>
        /// <param name="pcmDataLength">Length of the PCM data in bytes.</param>
        /// <param name="sampleRate">Sample rate of the audio (e.g., 24000).</param>
        /// <returns>44-byte WAV header as byte array.</returns>
        private byte[] GenerateWavHeader(int pcmDataLength, int sampleRate)
        {
            const int numChannels = 1;      // Mono
            const int bitsPerSample = 16;   // 16-bit PCM
            var byteRate = sampleRate * numChannels * bitsPerSample / 8;
            const int blockAlign = numChannels * bitsPerSample / 8;
            var chunkSize = 36 + pcmDataLength;

            var header = new byte[44];
            var offset = 0;

            // RIFF header (0-3): "RIFF"
            var riffBytes = Encoding.ASCII.GetBytes("RIFF");
            Array.Copy(riffBytes, 0, header, offset, 4);
            offset += 4;

            // ChunkSize (4-7): 36 + pcmDataLength (little-endian)
            var chunkSizeBytes = BitConverter.GetBytes(chunkSize);
            Array.Copy(chunkSizeBytes, 0, header, offset, 4);
            offset += 4;

            // Format (8-11): "WAVE"
            var waveBytes = Encoding.ASCII.GetBytes("WAVE");
            Array.Copy(waveBytes, 0, header, offset, 4);
            offset += 4;

            // Subchunk1 ID (12-15): "fmt "
            var fmtBytes = Encoding.ASCII.GetBytes("fmt ");
            Array.Copy(fmtBytes, 0, header, offset, 4);
            offset += 4;

            // Subchunk1Size (16-19): 16 for PCM (little-endian)
            var subchunk1SizeBytes = BitConverter.GetBytes(16);
            Array.Copy(subchunk1SizeBytes, 0, header, offset, 4);
            offset += 4;

            // AudioFormat (20-21): 1 for PCM (little-endian)
            var audioFormatBytes = BitConverter.GetBytes((short)1);
            Array.Copy(audioFormatBytes, 0, header, offset, 2);
            offset += 2;

            // NumChannels (22-23): 1 for mono (little-endian)
            var numChannelsBytes = BitConverter.GetBytes((short)numChannels);
            Array.Copy(numChannelsBytes, 0, header, offset, 2);
            offset += 2;

            // SampleRate (24-27): sample rate (little-endian)
            var sampleRateBytes = BitConverter.GetBytes(sampleRate);
            Array.Copy(sampleRateBytes, 0, header, offset, 4);
            offset += 4;

            // ByteRate (28-31): SampleRate * NumChannels * BitsPerSample/8 (little-endian)
            var byteRateBytes = BitConverter.GetBytes(byteRate);
            Array.Copy(byteRateBytes, 0, header, offset, 4);
            offset += 4;

            // BlockAlign (32-33): NumChannels * BitsPerSample/8 (little-endian)
            var blockAlignBytes = BitConverter.GetBytes((short)blockAlign);
            Array.Copy(blockAlignBytes, 0, header, offset, 2);
            offset += 2;

            // BitsPerSample (34-35): 16 (little-endian)
            var bitsPerSampleBytes = BitConverter.GetBytes((short)bitsPerSample);
            Array.Copy(bitsPerSampleBytes, 0, header, offset, 2);
            offset += 2;

            // Subchunk2 ID (36-39): "data"
            var dataBytes = Encoding.ASCII.GetBytes("data");
            Array.Copy(dataBytes, 0, header, offset, 4);
            offset += 4;

            // Subchunk2Size (40-43): pcmDataLength (little-endian)
            var subchunk2SizeBytes = BitConverter.GetBytes(pcmDataLength);
            Array.Copy(subchunk2SizeBytes, 0, header, offset, 4);

            return header;
        }

        /// <summary>
        /// Validates that the provided stream contains valid WAV audio data.
        /// </summary>
        /// <param name="audioStream">The audio stream to validate.</param>
        /// <returns>True if the stream appears to contain valid WAV data, false otherwise.</returns>
        private async Task<bool> ValidateWavStreamAsync(Stream audioStream)
        {
            if (audioStream == null || !audioStream.CanRead)
            {
                return false;
            }

            try
            {
                // Check minimum size for WAV header (44 bytes)
                if (audioStream.Length < 44)
                {
                    _logger.LogWarning("Audio stream too small ({Length} bytes). Minimum WAV file size is 44 bytes.", audioStream.Length);
                    return false;
                }

                // Reset stream position to beginning
                audioStream.Position = 0;
                
                // Read WAV header to validate format
                var headerBuffer = new byte[12];
                await audioStream.ReadExactlyAsync(headerBuffer, 0, 12);
                
                // Check RIFF signature
                var riffSignature = Encoding.ASCII.GetString(headerBuffer, 0, 4);
                var waveSignature = Encoding.ASCII.GetString(headerBuffer, 8, 4);
                
                if (riffSignature != "RIFF" || waveSignature != "WAVE")
                {
                    _logger.LogWarning("Invalid WAV file signature. Expected RIFF...WAVE, got {RiffSig}...{WaveSig}", 
                        riffSignature, waveSignature);
                    return false;
                }

                // Reset stream position for consumer
                audioStream.Position = 0;
                
                _logger.LogDebug("WAV stream validation successful. File size: {Length} bytes.", audioStream.Length);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error validating WAV stream: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Calculates the backoff delay for retry attempts using configurable exponential backoff.
        /// Uses the configured initial delay and multiplier from AppSettings.
        /// Each thread has its own independent backoff timer.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number (1-based).</param>
        /// <returns>The delay in milliseconds.</returns>
        /// <remarks>
        /// Backoff progression uses exponential backoff with configurable parameters:
        /// - Initial delay: TtsRetryInitialDelayMs (default: 30 seconds)
        /// - Multiplier: TtsRetryBackoffMultiplier (default: 2.0)
        /// - Maximum delay: 60 minutes (3600 seconds) - capped for safety
        ///
        /// Example with defaults (30s initial, 2.0 multiplier):
        /// - Attempt 1: 30 seconds
        /// - Attempt 2: 60 seconds (1 minute)
        /// - Attempt 3: 120 seconds (2 minutes)
        /// - Attempt 4: 240 seconds (4 minutes)
        /// - etc., up to 60 minutes maximum
        /// </remarks>
        internal int CalculateBackoffDelay(int attemptNumber)
        {
            // Use configurable exponential backoff
            var initialDelayMs = _appSettings.TtsRetryInitialDelayMs;
            var multiplier = _appSettings.TtsRetryBackoffMultiplier;

            // Calculate delay: initialDelay * multiplier^(attemptNumber - 1)
            var delayMs = initialDelayMs * Math.Pow(multiplier, attemptNumber - 1);

            // Cap at maximum of 60 minutes (3600 seconds) for safety
            var maxDelayMs = 60 * 60 * 1000; // 60 minutes in milliseconds
            var finalDelayMs = Math.Min(delayMs, maxDelayMs);

            return (int)finalDelayMs;
        }

        /// <summary>
        /// Loads the API key from environment variables or .env file.
        /// </summary>
        /// <returns>The API key if found, or null if not available.</returns>
        private string? LoadApiKeyFromEnv()
        {
            try
            {
                // First check if the environment variable is already set (useful for tests)
                var apiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
                if (!string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogDebug("Successfully loaded GEMINI_API_KEY from environment variable.");
                    return apiKey;
                }

                // If not found in environment, try to load from .env file
                var envPath = Path.Combine(AppContext.BaseDirectory, ".env");
                if (File.Exists(envPath))
                {
                    Env.Load(envPath);
                    apiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");

                    if (!string.IsNullOrEmpty(apiKey))
                    {
                        _logger.LogDebug("Successfully loaded GEMINI_API_KEY from .env file.");
                        return apiKey;
                    }
                    else
                    {
                        _logger.LogWarning("GEMINI_API_KEY found in .env file but is empty.");
                    }
                }
                else
                {
                    _logger.LogWarning(".env file not found at {EnvPath}.", envPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading API key: {Message}", ex.Message);
            }

            return null;
        }
    }
}
