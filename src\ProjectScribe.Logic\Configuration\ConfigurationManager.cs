using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
namespace ProjectScribe.Logic.Configuration
{
    /// <summary>
    /// Implementation of IConfigurationManager for loading and managing application configuration.
    /// Loads settings from config.json file with fallback to sensible defaults.
    /// </summary>
    public class ConfigurationManager : IConfigurationManager
    {
        private readonly AppSettings _appSettings;
        private readonly ILogger<ConfigurationManager>? _logger;

        /// <summary>
        /// Initializes a new instance of the ConfigurationManager class.
        /// Logger is optional during DI container setup phase.
        /// </summary>
        public ConfigurationManager()
        {
            // Note: Logger is not available during DI container setup, so it's optional
            _logger = null;
            _appSettings = LoadConfiguration();
        }

        /// <summary>
        /// Initializes a new instance of the ConfigurationManager class with a logger.
        /// Internal constructor for testing purposes.
        /// </summary>
        /// <param name="logger">Logger for configuration operations.</param>
        /// <exception cref="ArgumentNullException">Thrown when logger is null.</exception>
        internal ConfigurationManager(ILogger<ConfigurationManager> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _appSettings = LoadConfiguration();
        }

        private AppSettings LoadConfiguration()
        {
            var appSettings = new AppSettings(); // Initialize with POCO defaults first
            _logger?.LogTrace("Initialized AppSettings with default values");
            _logger?.LogDebug("Default MaxConcurrentTtsThreads: {MaxConcurrentTtsThreads}", appSettings.MaxConcurrentTtsThreads);

            var configFilePath = Path.Combine(AppContext.BaseDirectory, "config.json");
            _logger?.LogDebug("Looking for configuration file at: {ConfigFilePath}", configFilePath);
            _logger?.LogDebug("AppContext.BaseDirectory: {BaseDirectory}", AppContext.BaseDirectory);
            _logger?.LogDebug("Config file exists: {FileExists}", File.Exists(configFilePath));



            try
            {
                IConfigurationRoot configuration = new ConfigurationBuilder() // Explicit type
                    .SetBasePath(AppContext.BaseDirectory) // Ensures config.json is found in the execution directory
                    .AddJsonFile("config.json", optional: true, reloadOnChange: false) // Changed reloadOnChange to false
                    .Build();

                if (File.Exists(configFilePath))
                {
                    _logger?.LogInformation("Loading configuration from {ConfigFilePath}", configFilePath);

                    // Log the raw JSON content for debugging
                    var jsonContent = File.ReadAllText(configFilePath);
                    _logger?.LogDebug("Raw config JSON content: {JsonContent}", jsonContent);

                    configuration.Bind(appSettings); // Then bind from config, overwriting defaults where specified
                    _logger?.LogInformation("Configuration loaded successfully from file");

                    // Log key configuration values (without sensitive data)
                    _logger?.LogDebug("Configuration loaded - BaseLibraryDirectory: {BaseLibraryDirectory}, DefaultLogLevel: {DefaultLogLevel}, TextBlockTargetCharCount: {TextBlockTargetCharCount}, MaxConcurrentTtsThreads: {MaxConcurrentTtsThreads}",
                        appSettings.BaseLibraryDirectory, appSettings.DefaultLogLevel, appSettings.TextBlockTargetCharCount, appSettings.MaxConcurrentTtsThreads);
                }
                else
                {
                    _logger?.LogWarning("Configuration file not found at {ConfigFilePath}, using default values", configFilePath);
                }

                // Explicitly dispose the configuration object
                // IConfigurationRoot implements IDisposable
                (configuration as IDisposable)?.Dispose();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to load configuration from {ConfigFilePath}, using default values", configFilePath);

                // Use Console.WriteLine for critical configuration errors since logger might not be available
                Console.WriteLine($"[CONFIG ERROR] Failed to load configuration from '{configFilePath}': {ex.Message}");
                Console.WriteLine($"[CONFIG ERROR] Using default values. Check your config.json file for syntax errors.");

                // If configuration loading fails (e.g., invalid JSON), use defaults
                // appSettings is already initialized with defaults above
            }

            return appSettings;
        }
        /// <summary>
        /// Gets the loaded application settings.
        /// </summary>
        /// <returns>The current AppSettings instance with loaded configuration values.</returns>
        public AppSettings GetAppSettings()
        {
            return _appSettings;
        }
    }
}