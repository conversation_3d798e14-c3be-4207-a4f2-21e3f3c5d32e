using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Parsing.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace ProjectScribe.Logic.Text
{
    /// <summary>
    /// Implementation of text processing services for sanitizing and splitting chapter content.
    /// Handles Unicode sanitization and intelligent text splitting for TTS processing.
    /// </summary>
    public class TextProcessor : ITextProcessor
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly ILogger<TextProcessor> _logger;

        /// <summary>
        /// Initializes a new instance of the TextProcessor class.
        /// </summary>
        /// <param name="configurationManager">Configuration manager for accessing text processing settings.</param>
        /// <param name="logger">Logger for tracking text processing operations.</param>
        /// <exception cref="ArgumentNullException">Thrown when configurationManager or logger is null.</exception>
        public TextProcessor(IConfigurationManager configurationManager, ILogger<TextProcessor> logger)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Processes a chapter's content by sanitizing the text and splitting it into manageable blocks.
        /// </summary>
        /// <param name="chapterContent">The chapter content to process, containing title and raw text.</param>
        /// <param name="bookId">The unique identifier of the book this chapter belongs to.</param>
        /// <returns>A collection of text blocks ready for TTS processing, ordered by sequence.</returns>
        /// <exception cref="ArgumentNullException">Thrown when chapterContent or bookId is null.</exception>
        /// <exception cref="ArgumentException">Thrown when bookId is empty or whitespace.</exception>
        public IEnumerable<TextBlock> ProcessChapter(ChapterContent chapterContent, string bookId)
        {
            if (chapterContent == null)
                throw new ArgumentNullException(nameof(chapterContent));
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));

            _logger.LogDebug("Processing chapter '{ChapterTitle}' for book {BookId} (original length: {OriginalLength} chars)",
                chapterContent.Title, bookId, chapterContent.TextContent?.Length ?? 0);

            var appSettings = _configurationManager.GetAppSettings();
            var targetCharCount = appSettings.TextBlockTargetCharCount;

            _logger.LogTrace("Using target character count: {TargetCharCount}", targetCharCount);

            try
            {
                // Step 1: Sanitize the text
                _logger.LogTrace("Starting text sanitization for chapter '{ChapterTitle}'", chapterContent.Title);
                var sanitizedText = SanitizeText(chapterContent.TextContent ?? string.Empty);
                _logger.LogDebug("Text sanitization completed. Length changed from {OriginalLength} to {SanitizedLength} chars",
                    chapterContent.TextContent?.Length ?? 0, sanitizedText.Length);

                // Step 2: Split the text into blocks
                _logger.LogTrace("Starting text splitting into blocks");
                var textSegments = SplitTextIntoBlocks(sanitizedText, targetCharCount);
                _logger.LogDebug("Text split into {BlockCount} segments", textSegments.Count);

                // Step 3: Create TextBlock objects
                var textBlocks = new List<TextBlock>();
                for (int i = 0; i < textSegments.Count; i++)
                {
                    var textBlock = new TextBlock
                    {
                        BookId = bookId,
                        SequenceInChapter = i + 1, // 1-based sequence
                        OriginalChapterTitle = chapterContent.Title,
                        SanitizedText = textSegments[i]
                        // OutputTextFilePath will be set by BookProcessor
                    };
                    textBlocks.Add(textBlock);

                    _logger.LogTrace("Created text block {SequenceInChapter} with {CharCount} characters",
                        textBlock.SequenceInChapter, textBlock.SanitizedText.Length);
                }

                _logger.LogInformation("Successfully processed chapter '{ChapterTitle}' for book {BookId}: created {BlockCount} text blocks",
                    chapterContent.Title, bookId, textBlocks.Count);

                return textBlocks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process chapter '{ChapterTitle}' for book {BookId}",
                    chapterContent.Title, bookId);
                throw;
            }
        }

        /// <summary>
        /// Sanitizes text by removing or replacing problematic Unicode characters.
        /// </summary>
        /// <param name="text">The raw text to sanitize.</param>
        /// <returns>Sanitized text suitable for TTS processing.</returns>
        private string SanitizeText(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger.LogTrace("Input text is null or empty, returning empty string");
                return string.Empty;
            }

            _logger.LogTrace("Sanitizing text of {Length} characters", text.Length);

            var sb = new StringBuilder(text.Length);
            int controlCharsRemoved = 0;
            int surrogateCharsRemoved = 0;
            int privateUseCharsRemoved = 0;

            foreach (char c in text)
            {
                // Keep basic printable ASCII characters, whitespace, and common punctuation
                if (char.IsControl(c))
                {
                    // Replace control characters with appropriate substitutes
                    switch (c)
                    {
                        case '\r':
                        case '\n':
                        case '\t':
                            sb.Append(c); // Keep these whitespace characters
                            break;
                        default:
                            // Skip other control characters
                            controlCharsRemoved++;
                            break;
                    }
                }
                else if (char.IsHighSurrogate(c) || char.IsLowSurrogate(c))
                {
                    // Skip surrogate pairs that might cause issues
                    surrogateCharsRemoved++;
                    continue;
                }
                else if (char.GetUnicodeCategory(c) == UnicodeCategory.PrivateUse)
                {
                    // Skip private use characters
                    privateUseCharsRemoved++;
                    continue;
                }
                else
                {
                    sb.Append(c);
                }
            }

            if (controlCharsRemoved > 0 || surrogateCharsRemoved > 0 || privateUseCharsRemoved > 0)
            {
                _logger.LogDebug("Removed problematic characters: {ControlChars} control, {SurrogateChars} surrogate, {PrivateUseChars} private use",
                    controlCharsRemoved, surrogateCharsRemoved, privateUseCharsRemoved);
            }

            // Normalize whitespace
            var result = sb.ToString();
            var beforeWhitespaceNormalization = result.Length;
            result = Regex.Replace(result, @"[\r\n]+", "\n"); // Normalize line breaks first
            result = Regex.Replace(result, @"[ \t]+", " "); // Replace multiple spaces/tabs with single space, but preserve line breaks

            var finalResult = result.Trim();

            _logger.LogTrace("Text sanitization complete: {OriginalLength} -> {AfterCharRemoval} -> {AfterWhitespaceNorm} -> {FinalLength} characters",
                text.Length, beforeWhitespaceNormalization, result.Length, finalResult.Length);

            return finalResult;
        }

        /// <summary>
        /// Splits sanitized text into blocks based on paragraph and sentence boundaries.
        /// </summary>
        /// <param name="text">The sanitized text to split.</param>
        /// <param name="targetCharCount">Target character count per block.</param>
        /// <returns>List of text segments ready for TTS processing.</returns>
        private List<string> SplitTextIntoBlocks(string text, int targetCharCount)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger.LogTrace("Input text is empty, returning empty block list");
                return new List<string>();
            }

            _logger.LogTrace("Splitting text into blocks with target size {TargetCharCount}", targetCharCount);

            var blocks = new List<string>();

            // Split by paragraphs first
            var paragraphs = text.Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries)
                                .Select(p => p.Trim())
                                .Where(p => !string.IsNullOrEmpty(p))
                                .ToList();

            _logger.LogDebug("Found {ParagraphCount} paragraphs to process", paragraphs.Count);

            var currentBlock = new StringBuilder();
            int longParagraphsCount = 0;

            foreach (var paragraph in paragraphs)
            {
                // If adding this paragraph would exceed target, finalize current block
                if (currentBlock.Length > 0 &&
                    currentBlock.Length + paragraph.Length + 1 > targetCharCount)
                {
                    blocks.Add(currentBlock.ToString().Trim());
                    _logger.LogTrace("Finalized block {BlockNumber} with {CharCount} characters",
                        blocks.Count, currentBlock.Length);
                    currentBlock.Clear();
                }

                // If paragraph itself is too long, split it by sentences
                if (paragraph.Length > targetCharCount)
                {
                    longParagraphsCount++;
                    _logger.LogDebug("Paragraph {ParagraphLength} chars exceeds target, splitting by sentences",
                        paragraph.Length);

                    var sentenceBlocks = SplitParagraphBySentences(paragraph, targetCharCount);

                    // Add any existing content as a block first
                    if (currentBlock.Length > 0)
                    {
                        blocks.Add(currentBlock.ToString().Trim());
                        _logger.LogTrace("Finalized partial block {BlockNumber} with {CharCount} characters before adding sentence blocks",
                            blocks.Count, currentBlock.Length);
                        currentBlock.Clear();
                    }

                    blocks.AddRange(sentenceBlocks);
                    _logger.LogTrace("Added {SentenceBlockCount} sentence-based blocks", sentenceBlocks.Count);
                }
                else
                {
                    // Add paragraph to current block
                    if (currentBlock.Length > 0)
                        currentBlock.AppendLine();
                    currentBlock.Append(paragraph);
                }
            }

            // Add any remaining content
            if (currentBlock.Length > 0)
            {
                blocks.Add(currentBlock.ToString().Trim());
                _logger.LogTrace("Added final block with {CharCount} characters", currentBlock.Length);
            }

            if (longParagraphsCount > 0)
            {
                _logger.LogDebug("Split {LongParagraphCount} paragraphs that exceeded target size", longParagraphsCount);
            }

            _logger.LogDebug("Initial text splitting created {BlockCount} blocks", blocks.Count);

            // Normalize block sizes to avoid very small trailing blocks
            var normalizedBlocks = NormalizeBlockSizes(blocks, targetCharCount);

            _logger.LogDebug("After normalization: {FinalBlockCount} blocks", normalizedBlocks.Count);

            return normalizedBlocks;
        }

        /// <summary>
        /// Splits a long paragraph by sentence boundaries.
        /// </summary>
        /// <param name="paragraph">The paragraph to split.</param>
        /// <param name="targetCharCount">Target character count per block.</param>
        /// <returns>List of sentence-based text segments.</returns>
        private List<string> SplitParagraphBySentences(string paragraph, int targetCharCount)
        {
            _logger.LogTrace("Splitting paragraph of {Length} chars by sentences", paragraph.Length);

            var blocks = new List<string>();

            // Simple sentence splitting regex (can be improved for edge cases)
            var sentences = Regex.Split(paragraph, @"(?<=[.!?])\s+")
                                .Where(s => !string.IsNullOrWhiteSpace(s))
                                .ToList();

            _logger.LogTrace("Found {SentenceCount} sentences in paragraph", sentences.Count);

            var currentBlock = new StringBuilder();
            int veryLongSentences = 0;

            foreach (var sentence in sentences)
            {
                if (sentence.Length > targetCharCount)
                {
                    veryLongSentences++;
                    _logger.LogWarning("Found very long sentence ({Length} chars) that exceeds target block size",
                        sentence.Length);
                }

                // If adding this sentence would exceed target, finalize current block
                if (currentBlock.Length > 0 &&
                    currentBlock.Length + sentence.Length + 1 > targetCharCount)
                {
                    blocks.Add(currentBlock.ToString().Trim());
                    _logger.LogTrace("Finalized sentence block {BlockNumber} with {CharCount} characters",
                        blocks.Count, currentBlock.Length);
                    currentBlock.Clear();
                }

                // Add sentence to current block
                if (currentBlock.Length > 0)
                    currentBlock.Append(" ");
                currentBlock.Append(sentence);
            }

            // Add any remaining content
            if (currentBlock.Length > 0)
            {
                blocks.Add(currentBlock.ToString().Trim());
                _logger.LogTrace("Added final sentence block with {CharCount} characters", currentBlock.Length);
            }

            if (veryLongSentences > 0)
            {
                _logger.LogWarning("Paragraph contained {VeryLongSentenceCount} sentences exceeding target block size",
                    veryLongSentences);
            }

            _logger.LogTrace("Split paragraph into {BlockCount} sentence-based blocks", blocks.Count);

            return blocks;
        }

        /// <summary>
        /// Normalizes block sizes to avoid very small trailing blocks by merging them with previous blocks.
        /// </summary>
        /// <param name="blocks">The list of text blocks to normalize.</param>
        /// <param name="targetCharCount">Target character count per block.</param>
        /// <returns>Normalized list of text blocks.</returns>
        private List<string> NormalizeBlockSizes(List<string> blocks, int targetCharCount)
        {
            if (blocks.Count <= 1)
            {
                _logger.LogTrace("Only {BlockCount} block(s), no normalization needed", blocks.Count);
                return blocks;
            }

            _logger.LogTrace("Normalizing {BlockCount} blocks with target size {TargetCharCount}",
                blocks.Count, targetCharCount);

            var normalized = new List<string>();
            var minBlockSize = targetCharCount / 4; // Minimum 25% of target size
            int mergedBlocks = 0;

            for (int i = 0; i < blocks.Count; i++)
            {
                var currentBlock = blocks[i];

                // If this is the last block and it's too small, merge with previous
                if (i == blocks.Count - 1 &&
                    currentBlock.Length < minBlockSize &&
                    normalized.Count > 0)
                {
                    var lastBlock = normalized[normalized.Count - 1];
                    normalized[normalized.Count - 1] = lastBlock + "\n" + currentBlock;
                    mergedBlocks++;

                    _logger.LogTrace("Merged small final block ({CharCount} chars) with previous block",
                        currentBlock.Length);
                }
                else
                {
                    normalized.Add(currentBlock);
                }
            }

            if (mergedBlocks > 0)
            {
                _logger.LogDebug("Block normalization: merged {MergedCount} small blocks, final count: {FinalCount}",
                    mergedBlocks, normalized.Count);
            }
            else
            {
                _logger.LogTrace("No blocks needed merging during normalization");
            }

            return normalized;
        }
    }
}
