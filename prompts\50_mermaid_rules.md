**Mermaid Rules:**

1.  **Mermaid ONLY:** Use ```mermaid ... ``` code blocks.
2.  **Type:** Default `flowchart TD;` (or `graph TD;`).
3.  **Nodes:**
    *   IDs: Alphanumeric & underscore (`_`) ONLY. No spaces/other special chars.
    *   Text: Use `Node_ID["Display Text"]`. Quotes `""` for `Display Text` are VITAL if it contains spaces or special characters.
4.  **Connections:**
    *   Use `-->` ONLY. Example: `A --> B;`
    *   Labels: `A -- "Link Text" --> B;`. Quotes `""` for `Link Text` are VITAL if it has spaces/specials.
5.  **STRICTLY NO:** Styles, classes, subgraphs, comments, custom node shapes (use default rectangle), HTML entities.
6.  **Syntax:** End each statement line with a semicolon `;`.

Use mermaid for all diagrams.