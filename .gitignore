# Standard .NET Core ignores
## Build Artifacts
**/[Bb]in/
**/[Oo]bj/
# Visual Studio
*.vs/
*.suo
*.user
*.sln.docstates
# Rider
.idea/
*.iml
# User-specific files
*.userprefs
*.DS_Store
# Build results
[Dd]ebug/
[Rr]elease/
[Dd]ebugPublic/
[Rr]eleasePublic/
[Xx]64/
[Xx]86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
[Ll]ogs/
[Bb]uild/
[Bb]uild[Ll]og.*
*.VisualState.xml
TestResults/
[Bb]uild.proj
*.[Cc]ache
*.ncb
*.VC.db
*.pdb
# Project Scribe specific
logs/
data/project_scribe.db
.env
/test_library/tts_test_output.wav
/3rdparty/ffmpeg.exe
/test_library/output/Test_Audio_Book_part001.opus
/data/project_scribe.db-shm
/data/project_scribe.db-wal
/test_library/input/medium_book_test.epub
/test_library/
/config.json
