# ProjectScribe Console Application Build and Run Script
# This script builds the console application and runs it with proper cleanup

Write-Host "ProjectScribe Console Application Builder" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Kill any running instances first
Write-Host "Checking for running instances..." -ForegroundColor Yellow
try {
    $processes = Get-Process -Name "ProjectScribe.Console" -ErrorAction SilentlyContinue
    if ($processes) {
        Write-Host "Found $($processes.Count) running instance(s). Terminating..." -ForegroundColor Yellow
        $processes | Stop-Process -Force
        Start-Sleep -Seconds 2
        Write-Host "Running instances terminated." -ForegroundColor Green
    } else {
        Write-Host "No running instances found." -ForegroundColor Green
    }
} catch {
    Write-Host "No running instances to terminate." -ForegroundColor Green
}

# Build the application
Write-Host "Building ProjectScribe Console application..." -ForegroundColor Yellow
$buildResult = dotnet build src/ProjectScribe.Console/ProjectScribe.Console.csproj
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}
Write-Host "Build completed successfully!" -ForegroundColor Green
