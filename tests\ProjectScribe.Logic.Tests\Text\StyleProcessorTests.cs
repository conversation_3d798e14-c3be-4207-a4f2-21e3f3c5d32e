using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.State.Entities;
using ProjectScribe.Logic.Text;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Xunit;

namespace ProjectScribe.Logic.Tests.Text
{
    /// <summary>
    /// Unit tests for the StyleProcessor class.
    /// Tests style enhancement functionality and file operations.
    /// </summary>
    public class StyleProcessorTests : IDisposable
    {
        private readonly Mock<IConfigurationManager> _mockConfigManager;
        private readonly Mock<ILogger<StyleProcessor>> _mockLogger;
        private readonly StyleProcessor _styleProcessor;
        private readonly AppSettings _testAppSettings;
        private readonly string _testDirectory;

        public StyleProcessorTests()
        {
            _mockConfigManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<StyleProcessor>>();
            
            _testDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            _testAppSettings = new AppSettings
            {
                StylePreset = "STYLE:\nYou are Travis Baldree, professional audiobook narrator.\nTEXT:\n\n"
            };

            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(_testAppSettings);
            _styleProcessor = new StyleProcessor(_mockConfigManager.Object, _mockLogger.Object);
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        [Fact]
        public async Task ProcessTextBlocksAsync_WithValidTextBlocks_CreatesStyledFiles()
        {
            // Arrange
            var prepareDir = Path.Combine(_testDirectory, "prepare");
            var styledDir = Path.Combine(_testDirectory, "styled");
            Directory.CreateDirectory(prepareDir);

            var originalText = "Mirian woke abruptly, and then lay there, trying to figure out why.";
            var textFilePath = Path.Combine(prepareDir, "0001.txt");
            await File.WriteAllTextAsync(textFilePath, originalText);

            var textBlocks = new List<TextBlockState>
            {
                new TextBlockState
                {
                    TextBlockId = "block1",
                    BookId = "test-book",
                    BlockSequenceNumber = 1,
                    TextFilePath = textFilePath,
                    Status = TextBlockStatus.PendingStyleEnhancement
                }
            };

            // Act
            await _styleProcessor.ProcessTextBlocksAsync(textBlocks, "test-book", styledDir);

            // Assert
            var styledFilePath = Path.Combine(styledDir, "0001.txt");
            Assert.True(File.Exists(styledFilePath));

            var styledContent = await File.ReadAllTextAsync(styledFilePath);
            Assert.StartsWith(_testAppSettings.StylePreset, styledContent);
            Assert.EndsWith(originalText, styledContent);
        }

        [Fact]
        public async Task ProcessTextBlocksAsync_WithNullTextBlocks_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _styleProcessor.ProcessTextBlocksAsync(null!, "test-book", _testDirectory));
        }

        [Fact]
        public async Task ProcessTextBlocksAsync_WithEmptyBookId_ThrowsArgumentException()
        {
            // Arrange
            var textBlocks = new List<TextBlockState>();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _styleProcessor.ProcessTextBlocksAsync(textBlocks, "", _testDirectory));
        }

        [Fact]
        public async Task ProcessTextBlocksAsync_WithEmptyStyledDir_ThrowsArgumentException()
        {
            // Arrange
            var textBlocks = new List<TextBlockState>();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _styleProcessor.ProcessTextBlocksAsync(textBlocks, "test-book", ""));
        }

        [Fact]
        public async Task ProcessTextBlocksAsync_WithMissingTextFile_ThrowsFileNotFoundException()
        {
            // Arrange
            var styledDir = Path.Combine(_testDirectory, "styled");
            var textBlocks = new List<TextBlockState>
            {
                new TextBlockState
                {
                    TextBlockId = "block1",
                    BookId = "test-book",
                    BlockSequenceNumber = 1,
                    TextFilePath = Path.Combine(_testDirectory, "nonexistent.txt"),
                    Status = TextBlockStatus.PendingStyleEnhancement
                }
            };

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _styleProcessor.ProcessTextBlocksAsync(textBlocks, "test-book", styledDir));
        }
    }
}
