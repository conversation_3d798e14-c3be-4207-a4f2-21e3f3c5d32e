using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;
using Serilog;
using Serilog.Events;
using System;
using System.IO;
namespace ProjectScribe.Logic.Logging
{
    /// <summary>
    /// Static utility class for configuring Serilog logging with multiple file sinks and console output.
    /// Provides centralized logging setup based on application configuration settings.
    /// </summary>
    public static class SerilogSetup
    {
        /// <summary>
        /// Creates and configures a Serilog logger with multiple file sinks and console output.
        /// Sets up separate log files for different log levels (Information, Warning, Error, Debug, Verbose).
        /// </summary>
        /// <param name="configurationManager">Configuration manager to retrieve logging settings.</param>
        /// <returns>Configured Serilog logger instance.</returns>
        /// <exception cref="ArgumentNullException">Thrown when configurationManager is null.</exception>
        public static Serilog.Core.Logger CreateLogger(IConfigurationManager configurationManager)
        {
            var appSettings = configurationManager.GetAppSettings();
            var defaultLogLevelString = appSettings.DefaultLogLevel;
            if (!Enum.TryParse<LogEventLevel>(defaultLogLevelString, true, out var minimumLevel))
            {
                minimumLevel = LogEventLevel.Information; // Default to Information if parsing fails
                Console.WriteLine($"Warning: Could not parse DefaultLogLevel '{defaultLogLevelString}'. Defaulting to '{minimumLevel}'.");
            }
            var logDirectory = Path.Combine(AppContext.BaseDirectory, "logs");
            var loggerConfiguration = new LoggerConfiguration()
                .MinimumLevel.Is(minimumLevel)
                .Enrich.FromLogContext()
                .WriteTo.Console(restrictedToMinimumLevel: minimumLevel); // Console respects the minimum level
            // File sink for Information level messages
            loggerConfiguration.WriteTo.Logger(lc => lc
                .Filter.ByIncludingOnly(e => e.Level == LogEventLevel.Information)
                .WriteTo.File(Path.Combine(logDirectory, "Information-.log"),
                    rollingInterval: RollingInterval.Day,
                    restrictedToMinimumLevel: LogEventLevel.Information, // Ensure this sink only gets Info
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"));
            // File sink for Warning level messages
            loggerConfiguration.WriteTo.Logger(lc => lc
                .Filter.ByIncludingOnly(e => e.Level == LogEventLevel.Warning)
                .WriteTo.File(Path.Combine(logDirectory, "Warning-.log"),
                    rollingInterval: RollingInterval.Day,
                    restrictedToMinimumLevel: LogEventLevel.Warning, // Ensure this sink only gets Warning
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"));
            // File sink for Error and Fatal level messages
            loggerConfiguration.WriteTo.Logger(lc => lc
                .Filter.ByIncludingOnly(e => e.Level == LogEventLevel.Error || e.Level == LogEventLevel.Fatal)
                .WriteTo.File(Path.Combine(logDirectory, "Error-.log"),
                    rollingInterval: RollingInterval.Day,
                    restrictedToMinimumLevel: LogEventLevel.Error, // Ensure this sink only gets Error/Fatal
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"));
            // File sink for Debug level messages
            loggerConfiguration.WriteTo.Logger(lc => lc
                .Filter.ByIncludingOnly(e => e.Level == LogEventLevel.Debug)
                .WriteTo.File(Path.Combine(logDirectory, "Debug-.log"),
                    rollingInterval: RollingInterval.Day,
                    restrictedToMinimumLevel: LogEventLevel.Debug, // Ensure this sink only gets Debug
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"));
            // File sink for Verbose (Trace) level messages
            loggerConfiguration.WriteTo.Logger(lc => lc
                .Filter.ByIncludingOnly(e => e.Level == LogEventLevel.Verbose)
                .WriteTo.File(Path.Combine(logDirectory, "Verbose-.log"),
                    rollingInterval: RollingInterval.Day,
                    restrictedToMinimumLevel: LogEventLevel.Verbose, // Ensure this sink only gets Verbose
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"));
            return loggerConfiguration.CreateLogger();
        }
        /// <summary>
        /// Creates an ILoggerFactory configured to use the provided Serilog logger.
        /// This enables dependency injection of ILogger&lt;T&gt; instances throughout the application.
        /// </summary>
        /// <param name="serilogLogger">The configured Serilog logger to use as the provider.</param>
        /// <returns>ILoggerFactory configured with Serilog provider.</returns>
        /// <exception cref="ArgumentNullException">Thrown when serilogLogger is null.</exception>
        public static ILoggerFactory CreateLoggerFactory(Serilog.Core.Logger serilogLogger)
        {
            // Dispose previous logger if any, to prevent resource leaks if this is called multiple times.
            // However, typical DI setup registers this as a singleton, so Log.Logger might not be what we want to manage here.
            // If using global Log.Logger:
            // Log.CloseAndFlush(); 
            // Log.Logger = serilogLogger;
            // Create a new LoggerFactory and add Serilog.
            // This allows ILogger<T> to be injected.
            return new LoggerFactory().AddSerilog(serilogLogger, dispose: true); // dispose: true will dispose Serilog logger when LoggerFactory is disposed.
        }
    }
}