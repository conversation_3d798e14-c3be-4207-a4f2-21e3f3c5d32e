using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.State;
using ProjectScribe.Logic.State.Entities;
using Xunit;

namespace ProjectScribe.Logic.Tests.State
{
    /// <summary>
    /// Unit tests for the StateManager class.
    /// Tests basic state management operations using in-memory SQLite database.
    /// </summary>
    public class StateManagerTests : IDisposable
    {
        private readonly AppDbContext _dbContext;
        private readonly StateManager _stateManager;
        private readonly Mock<ILogger<StateManager>> _mockLogger;

        public StateManagerTests()
        {
            // Create in-memory SQLite database for testing
            var connection = new Microsoft.Data.Sqlite.SqliteConnection("Data Source=:memory:");
            connection.Open();

            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseSqlite(connection)
                .Options;

            _dbContext = new AppDbContext(options);
            _dbContext.Database.EnsureCreated();

            _mockLogger = new Mock<ILogger<StateManager>>();
            _stateManager = new StateManager(_dbContext, _mockLogger.Object);
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }

        [Fact]
        public async Task InitializeBookAsync_ShouldCreateNewBook_WhenBookDoesNotExist()
        {
            // Arrange
            var bookId = "test-book-001";
            var filePath = @"C:\test\sample.epub";

            // Act
            await _stateManager.InitializeBookAsync(bookId, filePath);

            // Assert
            var bookState = await _stateManager.GetBookStateAsync(bookId);
            Assert.NotNull(bookState);
            Assert.Equal(bookId, bookState.BookId);
            Assert.Equal(filePath, bookState.FilePath);
            Assert.Equal(BookProcessStatus.Pending, bookState.Status);
            Assert.Equal("Book initialized", bookState.LastProcessedStep);
        }

        [Fact]
        public async Task InitializeBookAsync_ShouldNotCreateDuplicate_WhenBookAlreadyExists()
        {
            // Arrange
            var bookId = "test-book-002";
            var filePath = @"C:\test\sample.epub";

            // Act
            await _stateManager.InitializeBookAsync(bookId, filePath);
            await _stateManager.InitializeBookAsync(bookId, filePath); // Second call

            // Assert
            var books = await _dbContext.BookProcessStates.Where(b => b.BookId == bookId).ToListAsync();
            Assert.Single(books);
        }

        [Fact]
        public async Task UpdateBookStatusAsync_ShouldUpdateBookStatus_WhenBookExists()
        {
            // Arrange
            var bookId = "test-book-003";
            var filePath = @"C:\test\sample.epub";
            await _stateManager.InitializeBookAsync(bookId, filePath);

            // Act
            await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingText, "Text processing started");

            // Assert
            var bookState = await _stateManager.GetBookStateAsync(bookId);
            Assert.NotNull(bookState);
            Assert.Equal(BookProcessStatus.ProcessingText, bookState.Status);
            Assert.Equal("Text processing started", bookState.LastProcessedStep);
        }

        [Fact]
        public async Task AddTextBlockAsync_ShouldCreateTextBlock_WhenValidData()
        {
            // Arrange
            var bookId = "test-book-004";
            var filePath = @"C:\test\sample.epub";
            await _stateManager.InitializeBookAsync(bookId, filePath);

            var textBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 1,
                TextFilePath = @"C:\test\blocks\block_001.txt",
                Status = TextBlockStatus.PendingTTS
            };

            // Act
            await _stateManager.AddTextBlockAsync(textBlock);

            // Assert
            var retrievedBlock = await _stateManager.GetTextBlockAsync(textBlock.TextBlockId);
            Assert.NotNull(retrievedBlock);
            Assert.Equal(bookId, retrievedBlock.BookId);
            Assert.Equal(1, retrievedBlock.BlockSequenceNumber);
            Assert.Equal(TextBlockStatus.PendingTTS, retrievedBlock.Status);
        }

        [Fact]
        public async Task GetPendingTextBlocksAsync_ShouldReturnOnlyPendingBlocks()
        {
            // Arrange
            var bookId = "test-book-005";
            var filePath = @"C:\test\sample.epub";
            await _stateManager.InitializeBookAsync(bookId, filePath);

            var pendingBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 1,
                TextFilePath = @"C:\test\blocks\block_001.txt",
                Status = TextBlockStatus.PendingTTS
            };

            var completedBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 2,
                TextFilePath = @"C:\test\blocks\block_002.txt",
                Status = TextBlockStatus.TTS_Success
            };

            await _stateManager.AddTextBlockAsync(pendingBlock);
            await _stateManager.AddTextBlockAsync(completedBlock);

            // Act
            var pendingBlocks = await _stateManager.GetPendingTextBlocksAsync(bookId);

            // Assert
            Assert.Single(pendingBlocks);
            Assert.Equal(pendingBlock.TextBlockId, pendingBlocks.First().TextBlockId);
        }

        [Fact]
        public async Task UpdateTextBlockStatusAsync_ShouldUpdateStatus_WhenBlockExists()
        {
            // Arrange
            var bookId = "test-book-006";
            var filePath = @"C:\test\sample.epub";
            await _stateManager.InitializeBookAsync(bookId, filePath);

            var textBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 1,
                TextFilePath = @"C:\test\blocks\block_001.txt",
                Status = TextBlockStatus.PendingTTS
            };

            await _stateManager.AddTextBlockAsync(textBlock);

            // Act
            await _stateManager.UpdateTextBlockStatusAsync(
                textBlock.TextBlockId, 
                TextBlockStatus.TTS_Success, 
                @"C:\test\wav\block_001.wav");

            // Assert
            var updatedBlock = await _stateManager.GetTextBlockAsync(textBlock.TextBlockId);
            Assert.NotNull(updatedBlock);
            Assert.Equal(TextBlockStatus.TTS_Success, updatedBlock.Status);
            Assert.Equal(@"C:\test\wav\block_001.wav", updatedBlock.WavFilePath);
        }

        [Fact]
        public async Task GetIncompleteBooksAsync_ShouldReturnOnlyIncompleteBooks()
        {
            // Arrange
            var completedBookId = "completed-book";
            var pendingBookId = "pending-book";
            var processingBookId = "processing-book";

            await _stateManager.InitializeBookAsync(completedBookId, @"C:\test\completed.epub");
            await _stateManager.InitializeBookAsync(pendingBookId, @"C:\test\pending.epub");
            await _stateManager.InitializeBookAsync(processingBookId, @"C:\test\processing.epub");

            await _stateManager.UpdateBookStatusAsync(completedBookId, BookProcessStatus.Completed);
            await _stateManager.UpdateBookStatusAsync(processingBookId, BookProcessStatus.ProcessingAudio);

            // Act
            var incompleteBooks = await _stateManager.GetIncompleteBooksAsync();

            // Assert
            Assert.Equal(2, incompleteBooks.Count());
            Assert.Contains(incompleteBooks, b => b.BookId == pendingBookId);
            Assert.Contains(incompleteBooks, b => b.BookId == processingBookId);
            Assert.DoesNotContain(incompleteBooks, b => b.BookId == completedBookId);
        }

        [Fact]
        public async Task BookExistsAsync_ShouldReturnTrue_WhenBookExists()
        {
            // Arrange
            var bookId = "existing-book";
            await _stateManager.InitializeBookAsync(bookId, @"C:\test\existing.epub");

            // Act
            var exists = await _stateManager.BookExistsAsync(bookId);

            // Assert
            Assert.True(exists);
        }

        [Fact]
        public async Task BookExistsAsync_ShouldReturnFalse_WhenBookDoesNotExist()
        {
            // Arrange
            var bookId = "non-existing-book";

            // Act
            var exists = await _stateManager.BookExistsAsync(bookId);

            // Assert
            Assert.False(exists);
        }

        [Fact]
        public async Task ResetPermanentlyFailedTextBlocksAsync_ShouldResetFailedBlocks_WhenPermanentlyFailedBlocksExist()
        {
            // Arrange
            var bookId = "test-book-reset";
            var filePath = @"C:\test\sample.epub";
            await _stateManager.InitializeBookAsync(bookId, filePath);

            var permanentlyFailedBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 1,
                TextFilePath = @"C:\test\blocks\block_001.txt",
                Status = TextBlockStatus.TTS_FailedPermanent,
                RetryCount = 3,
                ErrorMessage = "TTS failed after max retries",
                WavFilePath = @"C:\test\wav\invalid_block_001.wav"
            };

            var successfulBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 2,
                TextFilePath = @"C:\test\blocks\block_002.txt",
                Status = TextBlockStatus.TTS_Success,
                WavFilePath = @"C:\test\wav\block_002.wav"
            };

            await _stateManager.AddTextBlockAsync(permanentlyFailedBlock);
            await _stateManager.AddTextBlockAsync(successfulBlock);

            // Act
            var resetCount = await _stateManager.ResetPermanentlyFailedTextBlocksAsync(bookId);

            // Assert
            Assert.Equal(1, resetCount);

            var resetBlock = await _stateManager.GetTextBlockAsync(permanentlyFailedBlock.TextBlockId);
            Assert.NotNull(resetBlock);
            Assert.Equal(TextBlockStatus.PendingTTS, resetBlock.Status);
            Assert.Equal(0, resetBlock.RetryCount);
            Assert.Null(resetBlock.ErrorMessage);
            Assert.Null(resetBlock.WavFilePath);

            // Verify successful block was not affected
            var unchangedBlock = await _stateManager.GetTextBlockAsync(successfulBlock.TextBlockId);
            Assert.NotNull(unchangedBlock);
            Assert.Equal(TextBlockStatus.TTS_Success, unchangedBlock.Status);
            Assert.Equal(@"C:\test\wav\block_002.wav", unchangedBlock.WavFilePath);
        }

        [Fact]
        public async Task ResetPermanentlyFailedTextBlocksAsync_ShouldReturnZero_WhenNoPermanentlyFailedBlocks()
        {
            // Arrange
            var bookId = "test-book-no-failed";
            var filePath = @"C:\test\sample.epub";
            await _stateManager.InitializeBookAsync(bookId, filePath);

            var pendingBlock = new TextBlockState
            {
                BookId = bookId,
                BlockSequenceNumber = 1,
                TextFilePath = @"C:\test\blocks\block_001.txt",
                Status = TextBlockStatus.PendingTTS
            };

            await _stateManager.AddTextBlockAsync(pendingBlock);

            // Act
            var resetCount = await _stateManager.ResetPermanentlyFailedTextBlocksAsync(bookId);

            // Assert
            Assert.Equal(0, resetCount);
        }
    }
}
