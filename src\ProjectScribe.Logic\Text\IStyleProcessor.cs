using ProjectScribe.Logic.State.Entities;

namespace ProjectScribe.Logic.Text
{
    /// <summary>
    /// Interface for style processing services that enhance text blocks with style presets.
    /// Responsible for adding narrator instructions and style guidance to text blocks before TTS processing.
    /// </summary>
    public interface IStyleProcessor
    {
        /// <summary>
        /// Processes text blocks by adding style presets and saves them to styled text files.
        /// </summary>
        /// <param name="textBlocks">Collection of text blocks to enhance with style presets.</param>
        /// <param name="bookId">The unique identifier of the book these text blocks belong to.</param>
        /// <param name="styledDir">Directory path where styled text files should be saved.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        /// <exception cref="System.ArgumentNullException">Thrown when textBlocks, bookId, or styledDir is null.</exception>
        /// <exception cref="System.ArgumentException">Thrown when bookId or styledDir is empty or whitespace.</exception>
        Task ProcessTextBlocksAsync(IEnumerable<TextBlockState> textBlocks, string bookId, string styledDir);
    }
}
