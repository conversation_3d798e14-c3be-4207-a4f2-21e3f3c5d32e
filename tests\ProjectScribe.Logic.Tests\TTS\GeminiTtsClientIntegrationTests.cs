using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.TTS;
using Xunit;

namespace ProjectScribe.Logic.Tests.TTS
{
    /// <summary>
    /// Integration tests for GeminiTtsClient that test against the live API endpoint.
    /// These tests are rate-limited and should be run sparingly to avoid API abuse.
    /// </summary>
    public class GeminiTtsClientIntegrationTests : IDisposable
    {
        private readonly Mock<IConfigurationManager> _mockConfigManager;
        private readonly Mock<ILogger<GeminiTtsClient>> _mockLogger;
        private readonly HttpClient _httpClient;
        private readonly AppSettings _testAppSettings;

        public GeminiTtsClientIntegrationTests()
        {
            _mockConfigManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<GeminiTtsClient>>();
            _httpClient = new HttpClient();

            _testAppSettings = new AppSettings
            {
                GeminiTtsApiEndpoint = "https://gemini-openai-adapter.100169.xyz/tts",
                GeminiTtsFirstVoiceName = "Charon",
                GeminiTtsSecondVoiceName = "Zephyr",
                GeminiTtsModel = "gemini-2.5-flash-preview-tts",
                MaxTtsRetries = 2, // Reduced for integration tests
                TtsRetryInitialDelayMs = 1000,
                TtsRetryBackoffMultiplier = 2.0f
            };

            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(_testAppSettings);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task GetTtsAudioAsync_WithLiveEndpoint_ShortText_ReturnsValidAudio()
        {
            // Skip test if API key is not available
            var apiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
            if (string.IsNullOrEmpty(apiKey))
            {
                // Use Skip.If when available, or just return to skip the test
                return; // Skip test if no API key
            }

            // Arrange
            var testText = "Hello, this is a short test.";
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Length > 1000, "Audio file should be at least 1KB for meaningful content");
            
            // Verify it's a valid WAV file
            result.Position = 0;
            var headerBuffer = new byte[12];
            await result.ReadExactlyAsync(headerBuffer, 0, 12);
            
            var riffSignature = System.Text.Encoding.ASCII.GetString(headerBuffer, 0, 4);
            var waveSignature = System.Text.Encoding.ASCII.GetString(headerBuffer, 8, 4);
            
            Assert.Equal("RIFF", riffSignature);
            Assert.Equal("WAVE", waveSignature);

            result.Dispose();
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task GetTtsAudioAsync_WithLiveEndpoint_MediumText_ReturnsValidAudio()
        {
            // Skip test if API key is not available
            var apiKey = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
            if (string.IsNullOrEmpty(apiKey))
            {
                return; // Skip test if no API key
            }

            // Arrange
            var testText = "This is a longer test text to verify that the TTS service can handle medium-length content. " +
                          "The text should be long enough to generate a meaningful audio file that we can validate. " +
                          "This helps ensure the service works correctly with realistic text lengths.";
            
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Length > 5000, "Audio file should be at least 5KB for longer content");
            
            // Verify it's a valid WAV file
            result.Position = 0;
            var headerBuffer = new byte[12];
            await result.ReadExactlyAsync(headerBuffer, 0, 12);
            
            var riffSignature = System.Text.Encoding.ASCII.GetString(headerBuffer, 0, 4);
            var waveSignature = System.Text.Encoding.ASCII.GetString(headerBuffer, 8, 4);
            
            Assert.Equal("RIFF", riffSignature);
            Assert.Equal("WAVE", waveSignature);

            result.Dispose();
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task GetTtsAudioAsync_WithLiveEndpoint_InvalidApiKey_ReturnsNull()
        {
            // Arrange
            Environment.SetEnvironmentVariable("GEMINI_API_KEY", "invalid-api-key");
            var testText = "This should fail with invalid API key.";
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.Null(result);

            // Restore original API key if it existed
            var originalKey = GetOriginalApiKey();
            Environment.SetEnvironmentVariable("GEMINI_API_KEY", originalKey);
        }

        private static string? GetOriginalApiKey()
        {
            // Try to read from .env file if it exists
            try
            {
                var envPath = Path.Combine(AppContext.BaseDirectory, ".env");
                if (File.Exists(envPath))
                {
                    var lines = File.ReadAllLines(envPath);
                    var keyLine = lines.FirstOrDefault(line => line.StartsWith("GEMINI_API_KEY="));
                    if (keyLine != null)
                    {
                        return keyLine.Substring("GEMINI_API_KEY=".Length);
                    }
                }
            }
            catch
            {
                // Ignore errors reading .env file
            }
            return null;
        }
    }
}
