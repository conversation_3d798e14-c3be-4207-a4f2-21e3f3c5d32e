using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Audio;
using ProjectScribe.Logic.Common;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Parsing;
using ProjectScribe.Logic.Parsing.Models;
using ProjectScribe.Logic.State;
using ProjectScribe.Logic.State.Entities;
using ProjectScribe.Logic.Text;
using ProjectScribe.Logic.TTS;

namespace ProjectScribe.Logic.Processing
{
    /// <summary>
    /// Orchestrates the complete processing of a book from parsing through TTS conversion.
    /// Manages the workflow of converting input files to audio output with state tracking.
    /// </summary>
    public class BookProcessor : IBookProcessor
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly ILogger<BookProcessor> _logger;
        private readonly IStateManager _stateManager;
        private readonly IInputParser _inputParser;
        private readonly ITextProcessor _textProcessor;
        private readonly IStyleProcessor _styleProcessor;
        private readonly ITtsClient _ttsClient;
        private readonly IAudioPostProcessor _audioPostProcessor;

        /// <summary>
        /// Initializes a new instance of the BookProcessor class.
        /// </summary>
        public BookProcessor(
            IConfigurationManager configurationManager,
            ILogger<BookProcessor> logger,
            IStateManager stateManager,
            IInputParser inputParser,
            ITextProcessor textProcessor,
            IStyleProcessor styleProcessor,
            ITtsClient ttsClient,
            IAudioPostProcessor audioPostProcessor)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _stateManager = stateManager ?? throw new ArgumentNullException(nameof(stateManager));
            _inputParser = inputParser ?? throw new ArgumentNullException(nameof(inputParser));
            _textProcessor = textProcessor ?? throw new ArgumentNullException(nameof(textProcessor));
            _styleProcessor = styleProcessor ?? throw new ArgumentNullException(nameof(styleProcessor));
            _ttsClient = ttsClient ?? throw new ArgumentNullException(nameof(ttsClient));
            _audioPostProcessor = audioPostProcessor ?? throw new ArgumentNullException(nameof(audioPostProcessor));
        }

        /// <summary>
        /// Processes a book file through the complete pipeline: parsing, text processing, and TTS conversion.
        /// Supports resumability by checking existing state and continuing from the last completed step.
        /// </summary>
        /// <param name="filePath">Full path to the input book file.</param>
        public async Task ProcessBookAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be null or empty.", nameof(filePath));

            if (!File.Exists(filePath))
                throw new ArgumentException($"File does not exist: {filePath}", nameof(filePath));

            var appSettings = _configurationManager.GetAppSettings();
            var bookId = FileSystemUtils.GenerateBookId(filePath);

            _logger.LogInformation("Starting book processing for {BookId} from file {FilePath}", bookId, filePath);

            try
            {
                // Step 1: Book Identification and State Initialization
                var existingBookState = await InitializeBookProcessingAsync(bookId, filePath);

                // Step 2: Create Directory Structure
                var (prepareDir, styledDir, wavDir) = CreateDirectoryStructure(filePath, appSettings);

                // Step 3: Determine resume point and execute remaining steps
                await ResumeProcessingFromLastStep(existingBookState, filePath, bookId, prepareDir, styledDir, wavDir);

                _logger.LogInformation("Book processing completed successfully for {BookId}", bookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing book {BookId}", bookId);
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Failed, "Error", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Initializes book processing state in the database.
        /// Returns the existing book state if found, or null if a new book was initialized.
        /// </summary>
        private async Task<BookProcessState?> InitializeBookProcessingAsync(string bookId, string filePath)
        {
            _logger.LogDebug("Initializing book processing state for {BookId}", bookId);

            // Check if book already exists
            var existingBook = await _stateManager.GetBookStateAsync(bookId);
            if (existingBook == null)
            {
                await _stateManager.InitializeBookAsync(bookId, filePath);
                _logger.LogInformation("Initialized new book state for {BookId}", bookId);
                return null;
            }
            else
            {
                _logger.LogInformation("Book {BookId} already exists with status {Status}, will resume processing",
                    bookId, existingBook.Status);
                return existingBook;
            }
        }

        /// <summary>
        /// Creates the required directory structure for book processing.
        /// </summary>
        private (string prepareDir, string styledDir, string wavDir) CreateDirectoryStructure(string filePath, AppSettings appSettings)
        {
            var (author, series, bookName) = FileSystemUtils.ExtractPathComponents(filePath);

            var prepareBaseDir = Path.Combine(appSettings.BaseLibraryDirectory, appSettings.PrepareDirectoryName);
            var styledBaseDir = Path.Combine(appSettings.BaseLibraryDirectory, appSettings.StyledDirectoryName);
            var wavBaseDir = Path.Combine(appSettings.BaseLibraryDirectory, appSettings.WavDirectoryName);

            var prepareDir = FileSystemUtils.CombineAndEnsurePath(prepareBaseDir, author, series, bookName);
            var styledDir = FileSystemUtils.CombineAndEnsurePath(styledBaseDir, author, series, bookName);
            var wavDir = FileSystemUtils.CombineAndEnsurePath(wavBaseDir, author, series, bookName);

            _logger.LogDebug("Created directory structure - Prepare: {PrepareDir}, Styled: {StyledDir}, WAV: {WavDir}",
                prepareDir, styledDir, wavDir);

            return (prepareDir, styledDir, wavDir);
        }

        /// <summary>
        /// Determines the resume point based on existing book state and executes remaining processing steps.
        /// Verifies intermediate files exist before skipping steps to ensure robust recovery.
        /// </summary>
        private async Task ResumeProcessingFromLastStep(BookProcessState? existingBookState, string filePath, string bookId, string prepareDir, string styledDir, string wavDir)
        {
            if (existingBookState == null)
            {
                // New book - start from the beginning
                _logger.LogInformation("Starting fresh processing for new book {BookId}", bookId);
                await ProcessFromBeginning(filePath, bookId, prepareDir, styledDir, wavDir);
                return;
            }

            // Existing book - determine resume point
            _logger.LogInformation("Resuming processing for book {BookId} from status {Status}, last step: {LastStep}",
                bookId, existingBookState.Status, existingBookState.LastProcessedStep);

            switch (existingBookState.Status)
            {
                case BookProcessStatus.Pending:
                    // Book was initialized but processing never started
                    await ProcessFromBeginning(filePath, bookId, prepareDir, styledDir, wavDir);
                    break;

                case BookProcessStatus.ProcessingText:
                    // Check if text processing was completed by verifying text blocks exist
                    var existingTextBlocks = await _stateManager.GetTextBlocksForBookAsync(bookId);
                    if (existingTextBlocks.Any() && VerifyTextFilesExist(existingTextBlocks, prepareDir))
                    {
                        _logger.LogInformation("Text processing already completed for book {BookId}, proceeding to style enhancement", bookId);
                        await ProcessStyleEnhancementAsync(bookId, styledDir);
                        await ProcessTtsAsync(bookId, styledDir, wavDir);
                        await ProcessAudioPostProcessingAsync(bookId, filePath);
                        await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Completed, "AudioPostProcessing_Complete");
                    }
                    else
                    {
                        _logger.LogWarning("Text files missing or incomplete for book {BookId}, restarting text processing", bookId);
                        await ProcessFromTextProcessing(filePath, bookId, prepareDir, styledDir, wavDir);
                    }
                    break;

                case BookProcessStatus.ProcessingStyleEnhancement:
                    // Check if style enhancement was completed by verifying styled text blocks exist
                    var existingStyledBlocks = await _stateManager.GetTextBlocksForBookAsync(bookId);
                    if (existingStyledBlocks.Any() && VerifyStyledFilesExist(existingStyledBlocks, styledDir))
                    {
                        _logger.LogInformation("Style enhancement already completed for book {BookId}, proceeding to TTS", bookId);
                        await ProcessTtsAsync(bookId, styledDir, wavDir);
                        await ProcessAudioPostProcessingAsync(bookId, filePath);
                        await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Completed, "AudioPostProcessing_Complete");
                    }
                    else
                    {
                        _logger.LogWarning("Styled files missing or incomplete for book {BookId}, restarting style enhancement", bookId);
                        await ProcessStyleEnhancementAsync(bookId, styledDir);
                        await ProcessTtsAsync(bookId, styledDir, wavDir);
                        await ProcessAudioPostProcessingAsync(bookId, filePath);
                        await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Completed, "AudioPostProcessing_Complete");
                    }
                    break;

                case BookProcessStatus.ProcessingAudio:
                    // Reset any blocks that were left in TTS_InProgress state from interrupted processing
                    var resetCount = await _stateManager.ResetInProgressTextBlocksAsync(bookId);
                    if (resetCount > 0)
                    {
                        _logger.LogInformation("Reset {ResetCount} text blocks from TTS_InProgress to PendingTTS for book {BookId}", resetCount, bookId);
                    }

                    // Check if we have any successful TTS blocks
                    var successfulBlocks = await _stateManager.GetTextBlocksForBookByStatusAsync(bookId, TextBlockStatus.TTS_Success);
                    var pendingBlocks = await _stateManager.GetPendingTextBlocksAsync(bookId);
                    var failedBlocks = await _stateManager.GetTextBlocksForBookByStatusAsync(bookId, TextBlockStatus.TTS_Failed);

                    if (pendingBlocks.Any() || failedBlocks.Any())
                    {
                        _logger.LogInformation("Resuming TTS processing for book {BookId} - {PendingCount} pending, {FailedCount} failed blocks",
                            bookId, pendingBlocks.Count(), failedBlocks.Count());
                        await ProcessTtsAsync(bookId, styledDir, wavDir);
                    }

                    if (successfulBlocks.Any())
                    {
                        _logger.LogInformation("Proceeding to audio post-processing for book {BookId}", bookId);
                        await ProcessAudioPostProcessingAsync(bookId, filePath);
                        await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Completed, "AudioPostProcessing_Complete");
                    }
                    else
                    {
                        _logger.LogWarning("No successful TTS blocks found for book {BookId}, cannot proceed to audio post-processing", bookId);
                        await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Failed, "TTS", "No successful TTS blocks found");
                    }
                    break;

                case BookProcessStatus.Completed:
                    _logger.LogInformation("Book {BookId} is already completed, skipping processing", bookId);
                    break;

                case BookProcessStatus.Failed:
                    _logger.LogWarning("Book {BookId} previously failed with error: {Error}. Attempting to restart processing.",
                        bookId, existingBookState.ErrorMessage);
                    await ProcessFromBeginning(filePath, bookId, prepareDir, styledDir, wavDir);
                    break;

                default:
                    _logger.LogWarning("Unknown status {Status} for book {BookId}, restarting from beginning",
                        existingBookState.Status, bookId);
                    await ProcessFromBeginning(filePath, bookId, prepareDir, styledDir, wavDir);
                    break;
            }
        }

        /// <summary>
        /// Processes the book from the beginning (parsing through completion).
        /// </summary>
        private async Task ProcessFromBeginning(string filePath, string bookId, string prepareDir, string styledDir, string wavDir)
        {
            var chapters = await ParseBookAsync(filePath, bookId);
            if (!chapters.Any())
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Failed, "Parsing", "No chapters found in the book");
                return;
            }

            await ProcessTextAsync(chapters, bookId, prepareDir);
            await ProcessStyleEnhancementAsync(bookId, styledDir);
            await ProcessTtsAsync(bookId, styledDir, wavDir);
            await ProcessAudioPostProcessingAsync(bookId, filePath);
            await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Completed, "AudioPostProcessing_Complete");
        }

        /// <summary>
        /// Processes the book from text processing onwards (skipping parsing).
        /// </summary>
        private async Task ProcessFromTextProcessing(string filePath, string bookId, string prepareDir, string styledDir, string wavDir)
        {
            var chapters = await ParseBookAsync(filePath, bookId);
            if (!chapters.Any())
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Failed, "Parsing", "No chapters found in the book");
                return;
            }

            await ProcessTextAsync(chapters, bookId, prepareDir);
            await ProcessStyleEnhancementAsync(bookId, styledDir);
            await ProcessTtsAsync(bookId, styledDir, wavDir);
            await ProcessAudioPostProcessingAsync(bookId, filePath);
            await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Completed, "AudioPostProcessing_Complete");
        }

        /// <summary>
        /// Verifies that text files exist for all text blocks.
        /// </summary>
        private bool VerifyTextFilesExist(IEnumerable<TextBlockState> textBlocks, string prepareDir)
        {
            foreach (var textBlock in textBlocks)
            {
                if (string.IsNullOrEmpty(textBlock.TextFilePath) || !File.Exists(textBlock.TextFilePath))
                {
                    _logger.LogWarning("Text file missing for block {BlockId}: {FilePath}",
                        textBlock.TextBlockId, textBlock.TextFilePath);
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Verifies that styled text files exist for all text blocks.
        /// </summary>
        private bool VerifyStyledFilesExist(IEnumerable<TextBlockState> textBlocks, string styledDir)
        {
            foreach (var textBlock in textBlocks)
            {
                if (string.IsNullOrEmpty(textBlock.StyledTextFilePath) || !File.Exists(textBlock.StyledTextFilePath))
                {
                    _logger.LogWarning("Styled text file missing for block {BlockId}: {FilePath}",
                        textBlock.TextBlockId, textBlock.StyledTextFilePath);
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Parses the book file and extracts chapters.
        /// </summary>
        private async Task<IEnumerable<ChapterContent>> ParseBookAsync(string filePath, string bookId)
        {
            _logger.LogDebug("Parsing book {BookId} from {FilePath}", bookId, filePath);

            try
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingText, "Parsing");
                var chapters = await _inputParser.ParseAsync(filePath);

                _logger.LogInformation("Successfully parsed {ChapterCount} chapters from {BookId}", chapters.Count(), bookId);
                return chapters;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse book {BookId}", bookId);
                throw new InvalidOperationException($"Failed to parse book: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Processes text blocks by adding style presets and saves them to styled directory.
        /// </summary>
        private async Task ProcessStyleEnhancementAsync(string bookId, string styledDir)
        {
            _logger.LogDebug("Processing style enhancement for book {BookId}", bookId);

            try
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingStyleEnhancement, "StyleEnhancement");

                // Get all text blocks that need style enhancement
                var textBlocks = await _stateManager.GetTextBlocksForBookByStatusAsync(bookId, TextBlockStatus.PendingStyleEnhancement);
                var textBlocksList = textBlocks.ToList();

                if (!textBlocksList.Any())
                {
                    _logger.LogWarning("No text blocks found for style enhancement for book {BookId}", bookId);
                    return;
                }

                _logger.LogInformation("Processing style enhancement for {BlockCount} text blocks", textBlocksList.Count);

                // Process all text blocks through style enhancement
                await _styleProcessor.ProcessTextBlocksAsync(textBlocksList, bookId, styledDir);

                // Update text block states with styled file paths and new status
                foreach (var textBlock in textBlocksList)
                {
                    var originalFileName = Path.GetFileName(textBlock.TextFilePath);
                    var styledFilePath = Path.Combine(styledDir, originalFileName);

                    // Update the text block state with styled file path and new status
                    await _stateManager.UpdateTextBlockStatusAsync(textBlock.TextBlockId, TextBlockStatus.PendingTTS);

                    // Update the styled file path in the database
                    textBlock.StyledTextFilePath = styledFilePath;
                    // Note: We would need to add a method to StateManager to update the styled file path
                    // For now, we'll assume the TTS process will read from the styled directory
                }

                _logger.LogInformation("Style enhancement completed for book {BookId}. Enhanced {BlockCount} text blocks",
                    bookId, textBlocksList.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process style enhancement for book {BookId}", bookId);
                throw new InvalidOperationException($"Failed to process style enhancement: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Processes chapters into text blocks and saves them as .txt files.
        /// </summary>
        private async Task ProcessTextAsync(IEnumerable<ChapterContent> chapters, string bookId, string prepareDir)
        {
            _logger.LogDebug("Processing text for book {BookId}", bookId);

            try
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingText, "TextProcessing");

                int globalSequence = 1;

                foreach (var chapter in chapters)
                {
                    _logger.LogDebug("Processing chapter: {ChapterTitle}", chapter.Title);

                    // Process chapter into text blocks
                    var textBlocks = _textProcessor.ProcessChapter(chapter, bookId);

                    foreach (var textBlock in textBlocks)
                    {
                        // Generate file path for this text block
                        var fileName = $"{globalSequence:D4}.txt";
                        var textFilePath = Path.Combine(prepareDir, fileName);

                        // Save sanitized text to file
                        await File.WriteAllTextAsync(textFilePath, textBlock.SanitizedText);

                        // Update text block with file path
                        textBlock.OutputTextFilePath = textFilePath;

                        // Create TextBlockState and save to database
                        var textBlockState = new TextBlockState
                        {
                            TextBlockId = textBlock.BlockId,
                            BookId = bookId,
                            BlockSequenceNumber = globalSequence,
                            TextFilePath = textFilePath,
                            Status = TextBlockStatus.PendingStyleEnhancement
                        };

                        await _stateManager.AddTextBlockAsync(textBlockState);

                        _logger.LogDebug("Created text block {Sequence} for book {BookId}: {FilePath}",
                            globalSequence, bookId, textFilePath);

                        globalSequence++;
                    }
                }

                _logger.LogInformation("Text processing completed for book {BookId}. Created {BlockCount} text blocks",
                    bookId, globalSequence - 1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process text for book {BookId}", bookId);
                throw new InvalidOperationException($"Failed to process text: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Processes text blocks through TTS to generate WAV files.
        /// </summary>
        private async Task ProcessTtsAsync(string bookId, string styledDir, string wavDir)
        {
            _logger.LogDebug("Processing TTS for book {BookId}", bookId);

            try
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingAudio, "TTS");

                // Reset permanently failed blocks to allow retry on restart
                var resetPermanentlyFailedCount = await _stateManager.ResetPermanentlyFailedTextBlocksAsync(bookId);
                if (resetPermanentlyFailedCount > 0)
                {
                    _logger.LogInformation("Reset {Count} permanently failed text blocks to PendingTTS for book {BookId} - allowing retry on restart",
                        resetPermanentlyFailedCount, bookId);
                }

                var pendingBlocks = await _stateManager.GetPendingTextBlocksAsync(bookId);
                var appSettings = _configurationManager.GetAppSettings();

                _logger.LogInformation("Found {PendingCount} pending text blocks for TTS processing", pendingBlocks.Count());
                _logger.LogInformation("Using MaxConcurrentTtsThreads: {MaxConcurrentTtsThreads}", appSettings.MaxConcurrentTtsThreads);

                // Process blocks with concurrency control
                var semaphore = new SemaphoreSlim(appSettings.MaxConcurrentTtsThreads, appSettings.MaxConcurrentTtsThreads);
                var tasks = pendingBlocks.Select(async block =>
                {
                    await semaphore.WaitAsync();
                    _logger.LogDebug("Starting TTS processing for block {BlockId} (sequence {Sequence}) - Thread {ThreadId}",
                        block.TextBlockId, block.BlockSequenceNumber, Environment.CurrentManagedThreadId);
                    try
                    {
                        await ProcessSingleTextBlockAsync(block, styledDir, wavDir, appSettings);
                    }
                    finally
                    {
                        _logger.LogDebug("Completed TTS processing for block {BlockId} (sequence {Sequence}) - Thread {ThreadId}",
                            block.TextBlockId, block.BlockSequenceNumber, Environment.CurrentManagedThreadId);
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(tasks);

                _logger.LogInformation("TTS processing completed for book {BookId}", bookId);

                // Check if any blocks failed permanently during this TTS run
                var permanentlyFailedCount = await _stateManager.GetTextBlockCountByStatusAsync(bookId, TextBlockStatus.TTS_FailedPermanent);
                if (permanentlyFailedCount > 0)
                {
                    var errorMessage = $"TTS processing failed: {permanentlyFailedCount} text blocks failed permanently after {appSettings.MaxTtsRetries} retry attempts. Application will stop to prevent proceeding to next stage.";
                    _logger.LogError(errorMessage);
                    await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.Failed, "TTS", errorMessage);
                    throw new InvalidOperationException(errorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process TTS for book {BookId}", bookId);
                throw new InvalidOperationException($"Failed to process TTS: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Processes a single text block through TTS conversion.
        /// </summary>
        private async Task ProcessSingleTextBlockAsync(TextBlockState textBlock, string styledDir, string wavDir, AppSettings appSettings)
        {
            try
            {
                _logger.LogDebug("Processing TTS for text block {BlockId} (sequence {Sequence})",
                    textBlock.TextBlockId, textBlock.BlockSequenceNumber);

                // Update status to in progress
                await _stateManager.UpdateTextBlockStatusAsync(textBlock.TextBlockId, TextBlockStatus.TTS_InProgress);

                // Read styled text from file
                var originalFileName = Path.GetFileName(textBlock.TextFilePath);
                var styledFilePath = Path.Combine(styledDir, originalFileName);

                if (!File.Exists(styledFilePath))
                {
                    throw new FileNotFoundException($"Styled text file not found: {styledFilePath}");
                }

                var textContent = await File.ReadAllTextAsync(styledFilePath);
                if (string.IsNullOrWhiteSpace(textContent))
                {
                    throw new InvalidOperationException("Text file is empty or contains only whitespace");
                }

                // Calculate actual text length for validation (excluding StylePreset)
                var stylePresetLength = appSettings.StylePreset.Length;
                var actualTextLength = Math.Max(0, textContent.Length - stylePresetLength);

                // Call TTS service
                _logger.LogInformation("\x1b[34m↑[{Sequence}]↑\x1b[0m Starting TTS conversion for text block {BlockId} (sequence \x1b[95m{Sequence}\x1b[0m) with \x1b[95m{TotalCharCount}\x1b[0m total characters (\x1b[95m{ActualTextLength}\x1b[0m actual text, \x1b[95m{StylePresetLength}\x1b[0m style preset)",
                    textBlock.BlockSequenceNumber, textBlock.TextBlockId, textBlock.BlockSequenceNumber, textContent.Length, actualTextLength, stylePresetLength);
                using var audioStream = await _ttsClient.GetTtsAudioAsync(textContent);

                if (audioStream != null)
                {
                    // Generate WAV file path
                    var wavFileName = $"{textBlock.BlockSequenceNumber:D4}.wav";
                    var wavFilePath = Path.Combine(wavDir, wavFileName);

                    // Save WAV data to file
                    using (var fileStream = File.Create(wavFilePath))
                    {
                        await audioStream.CopyToAsync(fileStream);
                    }

                    // Verify WAV file with enhanced validation using actual text length (excluding StylePreset)
                    var validationResult = VerifyWavFile(wavFilePath, actualTextLength);
                    if (validationResult.IsValid)
                    {
                        // Update text block state - success
                        await _stateManager.UpdateTextBlockStatusAsync(textBlock.TextBlockId, TextBlockStatus.TTS_Success, wavFilePath, null, null);

                        _logger.LogInformation("\x1b[33m↓[{Sequence}]↓\x1b[0m Successfully completed TTS conversion for text block {BlockId} (sequence \x1b[95m{Sequence}\x1b[0m): {WavPath}",
                            textBlock.BlockSequenceNumber, textBlock.TextBlockId, textBlock.BlockSequenceNumber, wavFilePath);
                    }
                    else
                    {
                        // WAV verification failed - delete the invalid file
                        try
                        {
                            if (File.Exists(wavFilePath))
                            {
                                File.Delete(wavFilePath);
                                _logger.LogDebug("Deleted invalid WAV file: {WavPath}", wavFilePath);
                            }
                        }
                        catch (Exception deleteEx)
                        {
                            _logger.LogWarning(deleteEx, "Failed to delete invalid WAV file: {WavPath}", wavFilePath);
                        }

                        await HandleTtsFailure(textBlock, $"WAV file validation failed: {validationResult.ErrorMessage}", appSettings);
                    }
                }
                else
                {
                    // TTS returned null stream
                    await HandleTtsFailure(textBlock, "TTS service returned null audio stream", appSettings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing TTS for text block {BlockId}", textBlock.TextBlockId);
                await HandleTtsFailure(textBlock, ex.Message, appSettings);
            }
        }

        /// <summary>
        /// Handles TTS failure for a text block, including retry logic.
        /// </summary>
        private async Task HandleTtsFailure(TextBlockState textBlock, string errorMessage, AppSettings appSettings)
        {
            var newRetryCount = textBlock.RetryCount + 1;

            if (newRetryCount >= appSettings.MaxTtsRetries)
            {
                // Permanent failure
                await _stateManager.UpdateTextBlockStatusAsync(textBlock.TextBlockId, TextBlockStatus.TTS_FailedPermanent, null, newRetryCount, errorMessage);
                _logger.LogWarning("Text block {BlockId} failed permanently after {RetryCount} attempts: {Error}",
                    textBlock.TextBlockId, newRetryCount, errorMessage);
            }
            else
            {
                // Temporary failure, can be retried
                await _stateManager.UpdateTextBlockStatusAsync(textBlock.TextBlockId, TextBlockStatus.TTS_Failed, null, newRetryCount, errorMessage);
                _logger.LogWarning("Text block {BlockId} failed (attempt {RetryCount}/{MaxRetries}): {Error}",
                    textBlock.TextBlockId, newRetryCount, appSettings.MaxTtsRetries, errorMessage);
            }
        }

        /// <summary>
        /// Performs comprehensive verification of a WAV file including size validation based on text content.
        /// </summary>
        /// <param name="wavFilePath">Path to the WAV file to verify.</param>
        /// <param name="textCharacterCount">Number of characters in the source text.</param>
        /// <returns>Validation result with details about any failures.</returns>
        private static WavValidationResult VerifyWavFile(string wavFilePath, int textCharacterCount)
        {
            try
            {
                if (!File.Exists(wavFilePath))
                    return new WavValidationResult(false, "WAV file does not exist");

                var fileInfo = new FileInfo(wavFilePath);

                // Basic checks: file exists, not empty
                if (fileInfo.Length == 0)
                    return new WavValidationResult(false, "WAV file is empty");

                // WAV files should be at least 44 bytes (header size)
                if (fileInfo.Length < 44)
                    return new WavValidationResult(false, "WAV file is too small (less than header size)");

                // Check WAV header (first 4 bytes should be "RIFF")
                using var fileStream = File.OpenRead(wavFilePath);
                var headerBytes = new byte[4];
                fileStream.ReadExactly(headerBytes, 0, 4);
                var header = System.Text.Encoding.ASCII.GetString(headerBytes);

                if (header != "RIFF")
                    return new WavValidationResult(false, "Invalid WAV header (missing RIFF signature)");

                // Enhanced size validation based on text content
                // Rule: 1000 characters ≈ 3140 KB, with -40% to +50% tolerance
                // For very small text blocks (< 100 chars), use more lenient validation
                if (textCharacterCount >= 100)
                {
                    var expectedSizeBytes = (textCharacterCount / 1000.0) * 3140 * 1024;
                    var minSizeBytes = expectedSizeBytes * 0.60;
                    var maxSizeBytes = expectedSizeBytes * 1.50;

                    if (fileInfo.Length < minSizeBytes)
                    {
                        return new WavValidationResult(false,
                            $"WAV file too small: {fileInfo.Length:N0} bytes (expected {expectedSizeBytes:N0} ±35%, min {minSizeBytes:N0}) for {textCharacterCount} characters");
                    }

                    if (fileInfo.Length > maxSizeBytes)
                    {
                        return new WavValidationResult(false,
                            $"WAV file too large: {fileInfo.Length:N0} bytes (expected {expectedSizeBytes:N0} ±35%, max {maxSizeBytes:N0}) for {textCharacterCount} characters");
                    }
                }
                else
                {
                    // For small text blocks, just ensure the file is reasonably sized (at least 10KB, max 10MB)
                    if (fileInfo.Length < 10 * 1024)
                    {
                        return new WavValidationResult(false,
                            $"WAV file too small: {fileInfo.Length:N0} bytes (minimum 10KB for small text blocks)");
                    }

                    if (fileInfo.Length > 10 * 1024 * 1024)
                    {
                        return new WavValidationResult(false,
                            $"WAV file too large: {fileInfo.Length:N0} bytes (maximum 10MB for small text blocks)");
                    }
                }

                return new WavValidationResult(true, "WAV file validation passed");
            }
            catch (Exception ex)
            {
                return new WavValidationResult(false, $"WAV file validation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Result of WAV file validation.
        /// </summary>
        private record WavValidationResult(bool IsValid, string ErrorMessage);

        /// <summary>
        /// Processes audio post-processing by concatenating WAV files and converting to final format.
        /// </summary>
        private async Task ProcessAudioPostProcessingAsync(string bookId, string filePath)
        {
            _logger.LogDebug("Processing audio post-processing for book {BookId}", bookId);

            try
            {
                await _stateManager.UpdateBookStatusAsync(bookId, BookProcessStatus.ProcessingAudio, "AudioPostProcessing");

                // Get all successful WAV files for this book
                var successfulBlocks = await _stateManager.GetTextBlocksForBookByStatusAsync(bookId, TextBlockStatus.TTS_Success);
                var successfulBlocksList = successfulBlocks.ToList();

                if (!successfulBlocksList.Any())
                {
                    _logger.LogWarning("No successful WAV files found for book {BookId}. Skipping audio post-processing.", bookId);
                    return;
                }

                _logger.LogInformation("Found {SuccessfulCount} successful WAV files for audio post-processing", successfulBlocksList.Count);

                // Extract WAV file paths in correct sequence order
                var wavFilePaths = successfulBlocksList
                    .Where(block => !string.IsNullOrEmpty(block.WavFilePath))
                    .OrderBy(block => block.BlockSequenceNumber)
                    .Select(block => block.WavFilePath!)
                    .ToList();

                if (!wavFilePaths.Any())
                {
                    _logger.LogWarning("No valid WAV file paths found for book {BookId}. Skipping audio post-processing.", bookId);
                    return;
                }

                // Create output directory structure
                var outputDirectory = CreateOutputDirectory(filePath);
                var (_, _, bookName) = FileSystemUtils.ExtractPathComponents(filePath);

                // Group WAV files into batches based on duration limit
                var batches = GroupWavFilesByDuration(wavFilePaths, bookId);

                _logger.LogInformation("Processing {BatchCount} audio batches for book {BookId}", batches.Count, bookId);

                // Process each batch
                for (int i = 0; i < batches.Count; i++)
                {
                    var batch = batches[i];
                    var batchNumber = i + 1;

                    _logger.LogDebug("Processing audio batch {BatchNumber}/{TotalBatches} with {FileCount} WAV files",
                        batchNumber, batches.Count, batch.Count);

                    var finalAudioPath = await _audioPostProcessor.ProcessBatchAsync(
                        batch, outputDirectory, bookName, batchNumber);

                    if (!string.IsNullOrEmpty(finalAudioPath))
                    {
                        _logger.LogInformation("Successfully created final audio file for batch {BatchNumber}: {AudioPath}",
                            batchNumber, finalAudioPath);
                    }
                    else
                    {
                        _logger.LogError("Failed to create final audio file for batch {BatchNumber} of book {BookId}",
                            batchNumber, bookId);
                        // Continue processing other batches even if one fails
                    }
                }

                _logger.LogInformation("Audio post-processing completed for book {BookId}", bookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process audio post-processing for book {BookId}", bookId);
                throw new InvalidOperationException($"Failed to process audio post-processing: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates the output directory structure for the final audio files.
        /// </summary>
        private string CreateOutputDirectory(string filePath)
        {
            var appSettings = _configurationManager.GetAppSettings();
            var (author, series, bookName) = FileSystemUtils.ExtractPathComponents(filePath);

            var outputBaseDir = Path.Combine(appSettings.BaseLibraryDirectory, appSettings.OutputDirectoryName);
            var outputDirectory = FileSystemUtils.CombineAndEnsurePath(outputBaseDir, author, series, bookName);

            _logger.LogDebug("Created output directory: {OutputDirectory}", outputDirectory);

            return outputDirectory;
        }

        /// <summary>
        /// Groups WAV files into batches based on the maximum duration limit.
        /// For now, this is a simple implementation that groups by file count.
        /// In the future, this could be enhanced to consider actual audio duration.
        /// </summary>
        private List<List<string>> GroupWavFilesByDuration(List<string> wavFilePaths, string bookId)
        {
            var appSettings = _configurationManager.GetAppSettings();
            var batches = new List<List<string>>();

            // Simple batching strategy: assume each WAV file is approximately 1-2 minutes
            // and group them to stay under the MaxAudioBatchDurationMinutes limit
            var estimatedMinutesPerFile = 2; // Conservative estimate
            var maxFilesPerBatch = Math.Max(1, appSettings.MaxAudioBatchDurationMinutes / estimatedMinutesPerFile);

            _logger.LogDebug("Grouping {FileCount} WAV files into batches with max {MaxFiles} files per batch (target: {MaxMinutes} minutes)",
                wavFilePaths.Count, maxFilesPerBatch, appSettings.MaxAudioBatchDurationMinutes);

            for (int i = 0; i < wavFilePaths.Count; i += maxFilesPerBatch)
            {
                var batch = wavFilePaths.Skip(i).Take(maxFilesPerBatch).ToList();
                batches.Add(batch);
            }

            _logger.LogInformation("Created {BatchCount} batches for book {BookId}", batches.Count, bookId);

            return batches;
        }
    }
}
