namespace ProjectScribe.Logic.TTS
{
    /// <summary>
    /// Interface for Text-to-Speech client implementations.
    /// Provides methods to convert text to audio using TTS services.
    /// </summary>
    public interface ITtsClient
    {
        /// <summary>
        /// Converts the specified text to speech and returns the audio data as a stream.
        /// </summary>
        /// <param name="textToSynthesize">The text to convert to speech.</param>
        /// <returns>
        /// A stream containing WAV audio data if successful, or null if the conversion fails after all retries.
        /// The caller is responsible for disposing the returned stream.
        /// </returns>
        Task<Stream?> GetTtsAudioAsync(string textToSynthesize);
    }
}
