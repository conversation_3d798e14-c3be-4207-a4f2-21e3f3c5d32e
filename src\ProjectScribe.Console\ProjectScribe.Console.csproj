﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\ProjectScribe.Logic\ProjectScribe.Logic.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="SimpleInjector" Version="5.5.0" />
  </ItemGroup>

  <!-- Copy configuration files from root to output directory -->
  <ItemGroup>
    <Content Include="../../config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="../../config.json.example">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="../../.env">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="../../.env.example">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>    
  </PropertyGroup>

  <!-- Pre-build event to kill any running instances -->
  <Target Name="KillRunningInstances" BeforeTargets="Build">
    <Exec Command="powershell -Command &quot;Get-Process -Name 'ProjectScribe.Console' -ErrorAction SilentlyContinue | Stop-Process -Force; exit 0&quot;" ContinueOnError="true" />
  </Target>

</Project>
