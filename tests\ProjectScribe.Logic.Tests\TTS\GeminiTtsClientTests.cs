using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.TTS;
using System.Net;
using System.Text;
using Xunit;

namespace ProjectScribe.Logic.Tests.TTS
{
    public class GeminiTtsClientTests : IDisposable
    {
        private readonly Mock<IConfigurationManager> _mockConfigManager;
        private readonly Mock<ILogger<GeminiTtsClient>> _mockLogger;
        private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private readonly HttpClient _httpClient;
        private readonly AppSettings _testAppSettings;

        public GeminiTtsClientTests()
        {
            _mockConfigManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<GeminiTtsClient>>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            _testAppSettings = new AppSettings
            {
                GeminiTtsApiEndpoint = "https://test-api.example.com/tts",
                GeminiTtsFirstVoiceName = "TestVoice1",
                GeminiTtsSecondVoiceName = "TestVoice2",
                GeminiTtsModel = "test-model",
                MaxTtsRetries = 3,
                TtsRetryInitialDelayMs = 100, // Short delay for tests
                TtsRetryBackoffMultiplier = 2.0f
            };

            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(_testAppSettings);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithValidText_ReturnsAudioStream()
        {
            // Arrange
            var testText = "Hello, world!";
            var mockWavData = CreateMockWavData();

            SetupSuccessfulHttpResponse(mockWavData);
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");

            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Length > 0);
            Assert.Equal(mockWavData.Length, result.Length);

            // Verify the stream contains WAV data
            result.Position = 0;
            var buffer = new byte[4];
            await result.ReadExactlyAsync(buffer, 0, 4);
            var signature = Encoding.ASCII.GetString(buffer);
            Assert.Equal("RIFF", signature);

            result.Dispose();
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithEmptyText_ReturnsNull()
        {
            // Arrange
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync("");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithNullText_ReturnsNull()
        {
            // Arrange
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(null!);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithMissingApiKey_ReturnsNull()
        {
            // Arrange
            SetupEnvironmentVariable("GEMINI_API_KEY", null);
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync("Test text");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithHttpError_RetriesAndReturnsNull()
        {
            // Arrange
            var testText = "Hello, world!";
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");

            // Setup HTTP to return error responses
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Internal server error")
                });

            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.Null(result);

            // Verify that retries were attempted
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Exactly(_testAppSettings.MaxTtsRetries),
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>());
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithTransientErrorThenSuccess_ReturnsAudioStream()
        {
            // Arrange
            var testText = "Hello, world!";
            var mockWavData = CreateMockWavData();
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");

            var callCount = 0;
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(() =>
                {
                    callCount++;
                    if (callCount == 1)
                    {
                        // First call fails
                        return new HttpResponseMessage(HttpStatusCode.ServiceUnavailable);
                    }
                    else
                    {
                        // Second call succeeds - return base64 PCM data
                        var pcmData = mockWavData.Skip(44).ToArray();
                        var base64Audio = Convert.ToBase64String(pcmData);
                        var content = new StringContent(base64Audio);
                        var response = new HttpResponseMessage(HttpStatusCode.OK)
                        {
                            Content = content
                        };
                        response.Headers.Add("X-Sample-Rate", "24000");
                        return response;
                    }
                });

            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, callCount); // Should have made 2 calls
            result.Dispose();
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithInvalidBase64Data_ReturnsNull()
        {
            // Arrange
            var testText = "Hello, world!";
            var invalidBase64 = "This is not valid base64 data!@#$%";
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(() =>
                {
                    var content = new StringContent(invalidBase64);
                    var response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = content
                    };
                    response.Headers.Add("X-Sample-Rate", "24000");
                    return response;
                });

            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTtsAudioAsync_WithMissingSampleRateHeader_ReturnsNull()
        {
            // Arrange
            var testText = "Hello, world!";
            SetupEnvironmentVariable("GEMINI_API_KEY", "test-api-key");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(() =>
                {
                    var content = new StringContent("dGVzdCBhdWRpbyBkYXRh"); // base64 test data
                    var response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = content
                    };
                    // Missing X-Sample-Rate header
                    return response;
                });

            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var result = await client.GetTtsAudioAsync(testText);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void Constructor_WithNullConfigurationManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new GeminiTtsClient(null!, _mockLogger.Object, _httpClient));
        }

        [Fact]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new GeminiTtsClient(_mockConfigManager.Object, null!, _httpClient));
        }

        [Fact]
        public void Constructor_WithNullHttpClient_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, null!));
        }

        private void SetupSuccessfulHttpResponse(byte[] wavData)
        {
            // Extract PCM data from WAV (skip 44-byte header)
            var pcmData = wavData.Skip(44).ToArray();
            var base64Audio = Convert.ToBase64String(pcmData);

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(() =>
                {
                    var content = new StringContent(base64Audio);
                    var response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = content
                    };
                    // Add the required X-Sample-Rate header
                    response.Headers.Add("X-Sample-Rate", "24000");
                    return response;
                });
        }

        private static byte[] CreateMockWavData()
        {
            // Create a minimal valid WAV file header
            var wavHeader = new byte[44];
            
            // RIFF header
            Encoding.ASCII.GetBytes("RIFF").CopyTo(wavHeader, 0);
            BitConverter.GetBytes(36).CopyTo(wavHeader, 4); // File size - 8
            Encoding.ASCII.GetBytes("WAVE").CopyTo(wavHeader, 8);
            
            // fmt chunk
            Encoding.ASCII.GetBytes("fmt ").CopyTo(wavHeader, 12);
            BitConverter.GetBytes(16).CopyTo(wavHeader, 16); // fmt chunk size
            BitConverter.GetBytes((short)1).CopyTo(wavHeader, 20); // Audio format (PCM)
            BitConverter.GetBytes((short)1).CopyTo(wavHeader, 22); // Number of channels
            BitConverter.GetBytes(44100).CopyTo(wavHeader, 24); // Sample rate
            BitConverter.GetBytes(88200).CopyTo(wavHeader, 28); // Byte rate
            BitConverter.GetBytes((short)2).CopyTo(wavHeader, 32); // Block align
            BitConverter.GetBytes((short)16).CopyTo(wavHeader, 34); // Bits per sample
            
            // data chunk
            Encoding.ASCII.GetBytes("data").CopyTo(wavHeader, 36);
            BitConverter.GetBytes(0).CopyTo(wavHeader, 40); // Data size (0 for this test)
            
            return wavHeader;
        }

        private static void SetupEnvironmentVariable(string key, string? value)
        {
            Environment.SetEnvironmentVariable(key, value);
        }
    }
}
