using ProjectScribe.Logic.Parsing.Models;

namespace ProjectScribe.Logic.Parsing
{
    /// <summary>
    /// Interface for parsing input files and extracting chapter content.
    /// Implementations should handle specific file formats (e.g., EPUB, PDF, etc.).
    /// </summary>
    public interface IInputParser
    {
        /// <summary>
        /// Parses the specified input file and extracts chapter content.
        /// </summary>
        /// <param name="filePath">Full path to the input file to parse.</param>
        /// <returns>
        /// A collection of ChapterContent objects representing the chapters found in the file,
        /// ordered by their sequence in the original document.
        /// </returns>
        /// <exception cref="ArgumentException">Thrown when the file path is null or empty.</exception>
        /// <exception cref="FileNotFoundException">Thrown when the specified file does not exist.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the file format is not supported or corrupted.</exception>
        Task<IEnumerable<ChapterContent>> ParseAsync(string filePath);
    }
}
