using Microsoft.Extensions.Logging;

namespace ProjectScribe.Console.Services
{
    /// <summary>
    /// Ensures only one instance of the application can run at a time using a named semaphore.
    /// </summary>
    public class SingleInstanceService : IDisposable
    {
        private readonly ILogger<SingleInstanceService> _logger;
        private readonly string _semaphoreName;
        private Semaphore? _semaphore;
        private bool _hasAcquiredSemaphore;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the SingleInstanceService.
        /// </summary>
        /// <param name="logger">Logger for recording single instance operations.</param>
        public SingleInstanceService(ILogger<SingleInstanceService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _semaphoreName = "Global\\ProjectScribe_SingleInstance_Semaphore";
        }

        /// <summary>
        /// Attempts to acquire the single instance lock.
        /// </summary>
        /// <returns>True if the lock was acquired successfully, false if another instance is already running.</returns>
        public bool TryAcquireInstance()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SingleInstanceService));
            }

            if (_hasAcquiredSemaphore)
            {
                _logger.LogWarning("Single instance lock has already been acquired");
                return true;
            }

            try
            {
                _logger.LogDebug("Attempting to acquire single instance lock using semaphore: {SemaphoreName}", _semaphoreName);

                // Try to create or open the named semaphore
                // Initial count = 1, maximum count = 1 (only one instance allowed)
                _semaphore = new Semaphore(1, 1, _semaphoreName, out bool createdNew);

                // Try to acquire the semaphore with a very short timeout
                // If another instance is running, this will return false immediately
                _hasAcquiredSemaphore = _semaphore.WaitOne(TimeSpan.FromMilliseconds(100));

                if (_hasAcquiredSemaphore)
                {
                    _logger.LogInformation("Successfully acquired single instance lock. Application can start.");
                    if (createdNew)
                    {
                        _logger.LogDebug("Created new semaphore for single instance control");
                    }
                    else
                    {
                        _logger.LogDebug("Opened existing semaphore for single instance control");
                    }
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to acquire single instance lock. Another instance of ProjectScribe is already running.");
                    _semaphore?.Dispose();
                    _semaphore = null;
                    return false;
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogError(ex, "Access denied when trying to create/access single instance semaphore. " +
                    "This may happen if running with different user privileges.");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error occurred while trying to acquire single instance lock");
                return false;
            }
        }

        /// <summary>
        /// Releases the single instance lock if it was acquired.
        /// </summary>
        public void ReleaseInstance()
        {
            if (_disposed)
            {
                return;
            }

            if (_hasAcquiredSemaphore && _semaphore != null)
            {
                try
                {
                    _semaphore.Release();
                    _hasAcquiredSemaphore = false;
                    _logger.LogInformation("Released single instance lock");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while releasing single instance lock");
                }
            }

            _semaphore?.Dispose();
            _semaphore = null;
        }

        /// <summary>
        /// Gets a value indicating whether the single instance lock has been acquired.
        /// </summary>
        public bool HasAcquiredInstance => _hasAcquiredSemaphore && !_disposed;

        /// <summary>
        /// Disposes the single instance service and releases any held locks.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            ReleaseInstance();
            _disposed = true;
        }
    }
}
