# Project Scribe: Directory and File Structure
**Version:** 1.0
**Date:** June 2, 2025
**Author:** <PERSON><PERSON><PERSON> Documenter
This document outlines the proposed directory and file structure for Project Scribe, a .NET Core console application designed to convert ePub files into audiobooks. The structure aims to promote clarity, maintainability, scalability, and testability, adhering to the specifications in [`prompts/03_directory_and_file_structure_v2.md`](prompts/03_directory_and_file_structure_v2.md:1) and the technical design detailed in [`docs/technical_design_project_scribe.md`](docs/technical_design_project_scribe.md:1).
## 1. Top-Level Project Files and Directories
The root of the project will contain configuration files, solution files, and essential documentation.
*   **`ProjectScribe/`** (Root Directory)
    *   **`.gitignore`**: Specifies intentionally untracked files that Git should ignore (e.g., build artifacts, user-specific files, `logs/`, `data/project_scribe.db` unless intentionally shared for dev).
        *   *Rationale:* Standard practice for all Git repositories to keep the repository clean.
    *   **`README.md`**: Provides an overview of the project, setup instructions, usage guidelines, and other essential information for developers and users.
        *   *Rationale:* Standard entry point for understanding and getting started with the project.
    *   **`ProjectScribe.sln`**: The Visual Studio solution file, grouping all related projects (`.csproj`) together.
        *   *Rationale:* Standard for .NET projects, enabling easy management in IDEs like Visual Studio or JetBrains Rider.
    *   **`.env.example`**: An example template for the `.env` file, which will store sensitive information like API keys (e.g., Gemini TTS API Key). The actual `.env` file should be in `.gitignore`.
        *   *Rationale:* Securely manages secrets by keeping them out of version control, as per [`prd/001_initial_prd.md:204`](prd/001_initial_prd.md:204).
    *   **`config.json.example`**: An example template for `config.json`, which stores operational parameters for the application (e.g., `InputDirectoryName`, `TextBlockTargetCharCount`, `ffmpeg` path). The actual `config.json` might be user-specific.
        *   *Rationale:* Provides a clear template for users to create their own `config.json` as required by [`FR4.1.1`](prd/001_initial_prd.md:103).
    *   **`src/`**: Contains all source code for the application, organized into distinct projects.
        *   *Rationale:* Standard convention for separating source code from other project files like documentation, tests, and scripts.
    *   **`tests/`**: Contains all test projects, mirroring the structure of the `src/` directory.
        *   *Rationale:* Centralizes tests, making them easy to find and run, promoting good testing practices.
    *   **`docs/`**: Contains all project documentation, including this file, technical designs, and user guides.
        *   *Rationale:* Central location for all project-related documentation.
    *   **`scripts/`**: Contains utility scripts for development, build, or deployment tasks (e.g., environment setup, ffmpeg checks).
        *   *Rationale:* Organizes automation and helper scripts.
    *   **`data/`**: (Optional, potentially gitignored) Used during development to store sample input files (e.g., ePubs) and the SQLite database file if not placed elsewhere.
        *   *Rationale:* Provides a convenient place for development-time data. The production SQLite database path might be configurable.
    *   **`.rooroo/`**: Contains Rooroo-specific task files and context.
        *   *Rationale:* Internal Rooroo operational directory.
## 2. `src/` Directory Structure
The `src/` directory houses the core application logic, split into a console application project and a class library for business logic.
*   **`src/`**
    *   **`ProjectScribe.Console/`**: The main executable project. This project is responsible for application startup, command-line argument parsing (if any), dependency injection setup, and orchestrating the main application flow by calling services from `ProjectScribe.Logic`.
        *   **`ProjectScribe.Console.csproj`**: The .NET project file.
        *   **`Program.cs`**: The main entry point of the application. Initializes configuration, logging, dependency injection, and starts the `DirectoryMonitor` or `BookProcessor`.
        *   **`AppHost.cs`** (Optional): Can be used to encapsulate application host setup, including DI container configuration (e.g., SimpleInject) and service registration, keeping `Program.cs` cleaner.
    *   **`ProjectScribe.Logic/`**: A class library containing all the core business logic, services, and components described in the technical design. This promotes separation of concerns and makes the logic reusable and testable independently of the console application.
        *   **`ProjectScribe.Logic.csproj`**: The .NET project file.
        *   **`Configuration/`**: Manages application configuration.
            *   `AppSettings.cs`: POCO class representing the structure of `config.json`.
            *   `ConfigurationManager.cs`: Implements `IConfigurationManager` to load and provide configuration settings.
            *   `IConfigurationManager.cs`: Interface for the configuration manager.
        *   **`Monitoring/`**: Handles directory monitoring for new input files.
            *   `DirectoryMonitor.cs`: Implements `IDirectoryMonitor` to watch the input directory.
            *   `IDirectoryMonitor.cs`: Interface for the directory monitor.
        *   **`Processing/`**: Orchestrates the book processing workflow.
            *   `BookProcessor.cs`: Implements `IBookProcessor`, coordinating tasks between parsers, text processors, TTS clients, and audio post-processors.
            *   `IBookProcessor.cs`: Interface for the book processor.
        *   **`Parsing/`**: Handles parsing of input files (e.g., ePub).
            *   `EpubInputParser.cs`: Implements `IInputParser` using the `EpubReader` library.
            *   `IInputParser.cs`: Interface for input file parsers.
        *   **`Text/`**: Manages text sanitization, splitting, and preparation.
            *   `TextProcessor.cs`: Implements `ITextProcessor`.
            *   `ITextProcessor.cs`: Interface for the text processor.
            *   `TextBlock.cs`: Represents a processed block of text ready for TTS.
        *   **`TTS/`**: Interacts with the Text-to-Speech service (Gemini API).
            *   `GeminiTtsClient.cs`: Implements `ITtsClient` for making requests to the Gemini TTS API.
            *   `ITtsClient.cs`: Interface for TTS clients.
            *   `TtsRequestOptions.cs`: POCO for TTS request parameters (voice, model, etc.).
        *   **`Audio/`**: Handles audio post-processing (concatenation, conversion).
            *   `AudioPostProcessor.cs`: Implements `IAudioPostProcessor`.
            *   `IAudioPostProcessor.cs`: Interface for audio post-processors.
            *   `FfmpegClient.cs`: Wrapper for executing `ffmpeg` commands.
        *   **`State/`**: Manages application state and resumability using SQLite.
            *   `StateManager.cs`: Implements `IStateManager`.
            *   `IStateManager.cs`: Interface for the state manager.
            *   `AppDbContext.cs`: (If using Entity Framework Core) SQLite `DbContext` for database interactions. Alternatively, a simpler SQLite helper class.
            *   `Migrations/` (If using EF Core): Contains database migration files.
                *   `V1__InitialCreate.cs` (Example migration)
            *   `Entities/`: POCO classes representing database table structures.
                *   `BookProcessState.cs`
                *   `TextBlockState.cs`
        *   **`Logging/`**: Configures and provides logging services.
            *   `SerilogSetup.cs`: Helper class to configure Serilog based on settings from `ConfigurationManager`.
        *   **`Common/`**: Shared utilities, constants, or base classes used across different modules within `ProjectScribe.Logic`.
            *   `FileSystemUtils.cs`: Utility functions for file system operations.
            *   `Constants.cs`: Application-wide constants.
        *   **`ExternalServices/`** (Alternative/Complementary to specific clients like `FfmpegClient`):
            *   `IFfmpegProcess.cs`: Interface defining interactions with an external ffmpeg process, could be implemented by `FfmpegClient`.
## 3. `tests/` Directory Structure
The `tests/` directory contains projects for unit, integration, and potentially end-to-end tests.
*   **`tests/`**
    *   **`ProjectScribe.Logic.Tests/`**: Unit tests for the `ProjectScribe.Logic` library. Each component/service in the logic library should have corresponding tests.
        *   `ProjectScribe.Logic.Tests.csproj`: The .NET test project file (e.g., using MSTest, NUnit, or xUnit).
        *   `Configuration/ConfigurationManagerTests.cs`
        *   `Processing/BookProcessorTests.cs`
        *   `Parsing/EpubInputParserTests.cs`
        *   `Text/TextProcessorTests.cs`
        *   `TTS/GeminiTtsClientTests.cs`
        *   `Audio/AudioPostProcessorTests.cs`
        *   `State/StateManagerTests.cs`
        *   *(Mocking libraries like Moq would be dependencies here)*
    *   **`ProjectScribe.Console.Tests/`**: (Optional but Recommended) Integration tests for the console application. These tests would verify the interaction between different components and the overall application flow.
        *   `ProjectScribe.Console.Tests.csproj`: The .NET test project file.
        *   `AppIntegrationTests.cs`: Tests covering end-to-end scenarios, possibly interacting with a test file system and mock external services.
## 4. Other Directories
*   **`docs/`**: (As described in Top-Level)
    *   `technical_design_project_scribe.md` (Existing)
    *   `directory_and_file_structure.md` (This file)
    *   `user_guide.md` (Potential future document)
*   **`scripts/`**: (As described in Top-Level)
    *   `setup-env.ps1`: PowerShell script to help initialize `.env` from `.env.example` or check for `ffmpeg` presence.
    *   `run-dev.ps1`: Script to build and run the application with development configurations.
*   **`data/`**: (As described in Top-Level, primarily for development)
    *   `project_scribe.db`: The SQLite database file used during development.
    *   `sample_books/`: Directory containing sample ePub files for testing.
        *   `example_book1.epub`
## 5. Text-based Tree Representation
```
ProjectScribe/
âââ .env.example
âââ .gitignore
âââ README.md
âââ ProjectScribe.sln
âââ config.json.example
âââ data/
â   âââ project_scribe.db
â   âââ sample_books/
â       âââ example_book1.epub
âââ docs/
â   âââ directory_and_file_structure.md
â   âââ technical_design_project_scribe.md
âââ scripts/
â   âââ run-dev.ps1
â   âââ setup-env.ps1
âââ src/
â   âââ ProjectScribe.Console/
â   â   âââ AppHost.cs
â   â   âââ Program.cs
â   â   âââ ProjectScribe.Console.csproj
â   âââ ProjectScribe.Logic/
â       âââ Audio/
â       â   âââ AudioPostProcessor.cs
â       â   âââ FfmpegClient.cs
â       â   âââ IAudioPostProcessor.cs
â       âââ Common/
â       â   âââ Constants.cs
â       â   âââ FileSystemUtils.cs
â       âââ Configuration/
â       â   âââ AppSettings.cs
â       â   âââ ConfigurationManager.cs
â       â   âââ IConfigurationManager.cs
â       âââ ExternalServices/
â       â   âââ IFfmpegProcess.cs
â       âââ Logging/
â       â   âââ SerilogSetup.cs
â       âââ Monitoring/
â       â   âââ DirectoryMonitor.cs
â       â   âââ IDirectoryMonitor.cs
â       âââ Parsing/
â       â   âââ EpubInputParser.cs
â       â   âââ IInputParser.cs
â       âââ Processing/
â       â   âââ BookProcessor.cs
â       â   âââ IBookProcessor.cs
â       âââ ProjectScribe.Logic.csproj
â       âââ State/
â       â   âââ AppDbContext.cs
â       â   âââ Entities/
â       â   â   âââ BookProcessState.cs
â       â   â   âââ TextBlockState.cs
â       â   âââ IStateManager.cs
â       â   âââ Migrations/
â       â   â   âââ V1__InitialCreate.cs
â       â   âââ StateManager.cs
â       âââ TTS/
â       â   âââ GeminiTtsClient.cs
â       â   âââ ITtsClient.cs
â       â   âââ TtsRequestOptions.cs
â       âââ Text/
â           âââ ITextProcessor.cs
â           âââ TextBlock.cs
â           âââ TextProcessor.cs
âââ tests/
    âââ ProjectScribe.Console.Tests/
    â   âââ AppIntegrationTests.cs
    â   âââ ProjectScribe.Console.Tests.csproj
    âââ ProjectScribe.Logic.Tests/
        âââ Audio/
        â   âââ AudioPostProcessorTests.cs
        âââ Configuration/
        â   âââ ConfigurationManagerTests.cs
        âââ Parsing/
        â   âââ EpubInputParserTests.cs
        âââ Processing/
        â   âââ BookProcessorTests.cs
        âââ ProjectScribe.Logic.Tests.csproj
        âââ State/
        â   âââ StateManagerTests.cs
        âââ TTS/
        â   âââ GeminiTtsClientTests.cs
        âââ Text/
            âââ TextProcessorTests.cs
```
## 6. Mermaid.js Graph Representation
```mermaid
graph TD;
    ProjectScribe["ProjectScribe/"];
    ProjectScribe --> dot_env_example[".env.example"];
    ProjectScribe --> dot_gitignore[".gitignore"];
    ProjectScribe --> README_md["README.md"];
    ProjectScribe --> ProjectScribe_sln["ProjectScribe.sln"];
    ProjectScribe --> config_json_example["config.json.example"];
    ProjectScribe --> data_dir["data/"];
        data_dir --> data_db["project_scribe.db"];
        data_dir --> data_sample_books["sample_books/"];
            data_sample_books --> example_epub["example_book1.epub"];
    ProjectScribe --> docs_dir["docs/"];
        docs_dir --> docs_dir_file_structure_md["directory_and_file_structure.md"];
        docs_dir --> docs_tech_design_md["technical_design_project_scribe.md"];
    ProjectScribe --> scripts_dir["scripts/"];
        scripts_dir --> scripts_run_dev_ps1["run-dev.ps1"];
        scripts_dir --> scripts_setup_env_ps1["setup-env.ps1"];
    ProjectScribe --> src_dir["src/"];
        src_dir --> src_Console["ProjectScribe.Console/"];
            src_Console --> src_Console_AppHost["AppHost.cs"];
            src_Console --> src_Console_Program["Program.cs"];
            src_Console --> src_Console_csproj["ProjectScribe.Console.csproj"];
        src_dir --> src_Logic["ProjectScribe.Logic/"];
            src_Logic --> src_Logic_Audio["Audio/"];
                src_Logic_Audio --> src_Logic_Audio_PostProcessor["AudioPostProcessor.cs"];
                src_Logic_Audio --> src_Logic_Audio_FfmpegClient["FfmpegClient.cs"];
                src_Logic_Audio --> src_Logic_Audio_IAudioPostProcessor["IAudioPostProcessor.cs"];
            src_Logic --> src_Logic_Common["Common/"];
                src_Logic_Common --> src_Logic_Common_Constants["Constants.cs"];
                src_Logic_Common --> src_Logic_Common_FileSystemUtils["FileSystemUtils.cs"];
            src_Logic --> src_Logic_Configuration["Configuration/"];
                src_Logic_Configuration --> src_Logic_Configuration_AppSettings["AppSettings.cs"];
                src_Logic_Configuration --> src_Logic_Configuration_ConfigManager["ConfigurationManager.cs"];
                src_Logic_Configuration --> src_Logic_Configuration_IConfigManager["IConfigurationManager.cs"];
            src_Logic --> src_Logic_ExternalServices["ExternalServices/"];
                src_Logic_ExternalServices --> src_Logic_ExternalServices_IFfmpeg["IFfmpegProcess.cs"];
            src_Logic --> src_Logic_Logging["Logging/"];
                src_Logic_Logging --> src_Logic_Logging_SerilogSetup["SerilogSetup.cs"];
            src_Logic --> src_Logic_Monitoring["Monitoring/"];
                src_Logic_Monitoring --> src_Logic_Monitoring_DirMonitor["DirectoryMonitor.cs"];
                src_Logic_Monitoring --> src_Logic_Monitoring_IDirMonitor["IDirectoryMonitor.cs"];
            src_Logic --> src_Logic_Parsing["Parsing/"];
                src_Logic_Parsing --> src_Logic_Parsing_EpubParser["EpubInputParser.cs"];
                src_Logic_Parsing --> src_Logic_Parsing_IInputParser["IInputParser.cs"];
            src_Logic --> src_Logic_Processing["Processing/"];
                src_Logic_Processing --> src_Logic_Processing_BookProcessor["BookProcessor.cs"];
                src_Logic_Processing --> src_Logic_Processing_IBookProcessor["IBookProcessor.cs"];
            src_Logic --> src_Logic_csproj["ProjectScribe.Logic.csproj"];
            src_Logic --> src_Logic_State["State/"];
                src_Logic_State --> src_Logic_State_AppDbContext["AppDbContext.cs"];
                src_Logic_State --> src_Logic_State_Entities["Entities/"];
                    src_Logic_State_Entities --> src_Logic_State_Entities_BookState["BookProcessState.cs"];
                    src_Logic_State_Entities --> src_Logic_State_Entities_BlockState["TextBlockState.cs"];
                src_Logic_State --> src_Logic_State_IStateManager["IStateManager.cs"];
                src_Logic_State --> src_Logic_State_Migrations["Migrations/"];
                    src_Logic_State_Migrations --> src_Logic_State_Migrations_V1["V1__InitialCreate.cs"];
                src_Logic_State --> src_Logic_State_StateManager["StateManager.cs"];
            src_Logic --> src_Logic_TTS["TTS/"];
                src_Logic_TTS --> src_Logic_TTS_GeminiClient["GeminiTtsClient.cs"];
                src_Logic_TTS --> src_Logic_TTS_ITtsClient["ITtsClient.cs"];
                src_Logic_TTS --> src_Logic_TTS_TtsOptions["TtsRequestOptions.cs"];
            src_Logic --> src_Logic_Text["Text/"];
                src_Logic_Text --> src_Logic_Text_ITextProcessor["ITextProcessor.cs"];
                src_Logic_Text --> src_Logic_Text_TextBlock["TextBlock.cs"];
                src_Logic_Text --> src_Logic_Text_TextProcessor["TextProcessor.cs"];
    ProjectScribe --> tests_dir["tests/"];
        tests_dir --> tests_Console["ProjectScribe.Console.Tests/"];
            tests_Console --> tests_Console_AppIntegration["AppIntegrationTests.cs"];
            tests_Console --> tests_Console_csproj["ProjectScribe.Console.Tests.csproj"];
        tests_dir --> tests_Logic["ProjectScribe.Logic.Tests/"];
            tests_Logic --> tests_Logic_Audio["Audio/"];
                tests_Logic_Audio --> tests_Logic_Audio_Tests["AudioPostProcessorTests.cs"];
            tests_Logic --> tests_Logic_Configuration["Configuration/"];
                tests_Logic_Configuration --> tests_Logic_Configuration_Tests["ConfigurationManagerTests.cs"];
            tests_Logic --> tests_Logic_Parsing["Parsing/"];
                tests_Logic_Parsing --> tests_Logic_Parsing_Tests["EpubInputParserTests.cs"];
            tests_Logic --> tests_Logic_Processing["Processing/"];
                tests_Logic_Processing --> tests_Logic_Processing_Tests["BookProcessorTests.cs"];
            tests_Logic --> tests_Logic_csproj["ProjectScribe.Logic.Tests.csproj"];
            tests_Logic --> tests_Logic_State["State/"];
                tests_Logic_State --> tests_Logic_State_Tests["StateManagerTests.cs"];
            tests_Logic --> tests_Logic_TTS["TTS/"];
                tests_Logic_TTS --> tests_Logic_TTS_Tests["GeminiTtsClientTests.cs"];
            tests_Logic --> tests_Logic_Text["Text/"];
                tests_Logic_Text --> tests_Logic_Text_Tests["TextProcessorTests.cs"];
```
This structure provides a solid foundation for Project Scribe, aligning with .NET best practices and the project's specific architectural requirements.