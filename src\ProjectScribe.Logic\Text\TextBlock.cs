using System;

namespace ProjectScribe.Logic.Text
{
    /// <summary>
    /// Represents a processed block of text ready for TTS processing.
    /// Contains sanitized text content and metadata for tracking and file management.
    /// </summary>
    public class TextBlock
    {
        /// <summary>
        /// Unique identifier for this text block, typically a GUID.
        /// Used to track processing state and correlate with database records.
        /// </summary>
        public string BlockId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Identifier of the book this text block belongs to.
        /// Used for organizing blocks and maintaining relationships.
        /// </summary>
        public string BookId { get; set; } = string.Empty;

        /// <summary>
        /// Sequential position of this block within the chapter (1-based).
        /// Used to maintain proper ordering during TTS processing and audio concatenation.
        /// </summary>
        public int SequenceInChapter { get; set; }

        /// <summary>
        /// Original title of the chapter this text block was extracted from.
        /// Preserved for context and debugging purposes.
        /// </summary>
        public string OriginalChapterTitle { get; set; } = string.Empty;

        /// <summary>
        /// The sanitized text content ready for TTS processing.
        /// Contains text with problematic Unicode characters removed or replaced.
        /// </summary>
        public string SanitizedText { get; set; } = string.Empty;

        /// <summary>
        /// File path where the sanitized text will be saved as a .txt file.
        /// Initially empty, will be populated by the BookProcessor when saving files.
        /// </summary>
        public string OutputTextFilePath { get; set; } = string.Empty;
    }
}
