using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.TTS;
using Xunit;

namespace ProjectScribe.Logic.Tests.TTS
{
    public class BackoffDelayTests : IDisposable
    {
        private readonly Mock<IConfigurationManager> _mockConfigManager;
        private readonly Mock<ILogger<GeminiTtsClient>> _mockLogger;
        private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private readonly HttpClient _httpClient;
        private readonly AppSettings _testAppSettings;

        public BackoffDelayTests()
        {
            _mockConfigManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<GeminiTtsClient>>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);

            _testAppSettings = new AppSettings
            {
                GeminiTtsApiEndpoint = "https://test-api.example.com/tts",
                GeminiTtsFirstVoiceName = "TestVoice1",
                GeminiTtsSecondVoiceName = "TestVoice2",
                GeminiTtsModel = "test-model",
                MaxTtsRetries = 3,
                TtsRetryInitialDelayMs = 100, // Short delay for tests
                TtsRetryBackoffMultiplier = 2.0f
            };

            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(_testAppSettings);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        [Fact]
        public void CalculateBackoffDelay_WithTestSettings_UsesConfiguredValues()
        {
            // Arrange
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act & Assert
            // With initial delay 100ms and multiplier 2.0:
            // Attempt 1: 100 * 2^0 = 100ms
            // Attempt 2: 100 * 2^1 = 200ms
            // Attempt 3: 100 * 2^2 = 400ms
            Assert.Equal(100, client.CalculateBackoffDelay(1));
            Assert.Equal(200, client.CalculateBackoffDelay(2));
            Assert.Equal(400, client.CalculateBackoffDelay(3));
        }

        [Fact]
        public void CalculateBackoffDelay_WithProductionSettings_UsesConfiguredValues()
        {
            // Arrange
            var productionSettings = new AppSettings
            {
                TtsRetryInitialDelayMs = 30000, // 30 seconds
                TtsRetryBackoffMultiplier = 2.0f
            };
            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(productionSettings);
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act & Assert
            // With initial delay 30000ms and multiplier 2.0:
            // Attempt 1: 30000 * 2^0 = 30000ms (30 seconds)
            // Attempt 2: 30000 * 2^1 = 60000ms (60 seconds)
            // Attempt 3: 30000 * 2^2 = 120000ms (120 seconds)
            Assert.Equal(30000, client.CalculateBackoffDelay(1));
            Assert.Equal(60000, client.CalculateBackoffDelay(2));
            Assert.Equal(120000, client.CalculateBackoffDelay(3));
        }

        [Fact]
        public void CalculateBackoffDelay_WithMaximumCap_CapsAt60Minutes()
        {
            // Arrange
            var highDelaySettings = new AppSettings
            {
                TtsRetryInitialDelayMs = 1000000, // 1000 seconds
                TtsRetryBackoffMultiplier = 10.0f
            };
            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(highDelaySettings);
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act
            var delay = client.CalculateBackoffDelay(5); // Would be 1000000 * 10^4 = 10,000,000,000ms without cap

            // Assert
            Assert.Equal(3600000, delay); // Should be capped at 60 minutes (3600000ms)
        }

        [Fact]
        public void CalculateBackoffDelay_WithDifferentMultiplier_UsesConfiguredMultiplier()
        {
            // Arrange
            var customSettings = new AppSettings
            {
                TtsRetryInitialDelayMs = 1000, // 1 second
                TtsRetryBackoffMultiplier = 1.5f
            };
            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(customSettings);
            var client = new GeminiTtsClient(_mockConfigManager.Object, _mockLogger.Object, _httpClient);

            // Act & Assert
            // With initial delay 1000ms and multiplier 1.5:
            // Attempt 1: 1000 * 1.5^0 = 1000ms
            // Attempt 2: 1000 * 1.5^1 = 1500ms
            // Attempt 3: 1000 * 1.5^2 = 2250ms
            Assert.Equal(1000, client.CalculateBackoffDelay(1));
            Assert.Equal(1500, client.CalculateBackoffDelay(2));
            Assert.Equal(2250, client.CalculateBackoffDelay(3));
        }
    }
}
