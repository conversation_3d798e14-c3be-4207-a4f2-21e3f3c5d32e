using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectScribe.Logic.Audio
{
    /// <summary>
    /// Implementation of IFfmpegClient for executing FFmpeg operations.
    /// Handles audio concatenation and format conversion using the FFmpeg external tool.
    /// </summary>
    public class FfmpegClient : IFfmpegClient
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly ILogger<FfmpegClient> _logger;

        /// <summary>
        /// Initializes a new instance of the FfmpegClient class.
        /// </summary>
        /// <param name="configurationManager">Configuration manager for accessing FFmpeg settings.</param>
        /// <param name="logger">Logger for recording FFmpeg operations and errors.</param>
        /// <exception cref="ArgumentNullException">Thrown when configurationManager or logger is null.</exception>
        public FfmpegClient(IConfigurationManager configurationManager, ILogger<FfmpegClient> logger)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Concatenates WAV files and converts them to the specified audio format
        /// </summary>
        /// <param name="wavFilePaths">List of WAV file paths to concatenate in order</param>
        /// <param name="outputFilePath">Path for the output audio file</param>
        /// <param name="finalAudioFormat">Target audio format (e.g., "opus", "mp3")</param>
        /// <param name="finalAudioBitrate">Target bitrate (e.g., "96k", "128k")</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ConcatenateAndConvertAsync(
            IEnumerable<string> wavFilePaths,
            string outputFilePath,
            string finalAudioFormat,
            string finalAudioBitrate)
        {
            return await ConcatenateAndConvertAsync(wavFilePaths, outputFilePath, finalAudioFormat, finalAudioBitrate, null);
        }

        /// <summary>
        /// Concatenates WAV files and converts them to the specified audio format with metadata
        /// </summary>
        /// <param name="wavFilePaths">List of WAV file paths to concatenate in order</param>
        /// <param name="outputFilePath">Path for the output audio file</param>
        /// <param name="finalAudioFormat">Target audio format (e.g., "opus", "mp3")</param>
        /// <param name="finalAudioBitrate">Target bitrate (e.g., "96k", "128k")</param>
        /// <param name="metadata">Optional metadata to embed in the audio file</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ConcatenateAndConvertAsync(
            IEnumerable<string> wavFilePaths,
            string outputFilePath,
            string finalAudioFormat,
            string finalAudioBitrate,
            AudioMetadata? metadata)
        {
            if (wavFilePaths == null || !wavFilePaths.Any())
            {
                _logger.LogError("No WAV files provided for concatenation");
                return false;
            }

            if (string.IsNullOrWhiteSpace(outputFilePath))
            {
                _logger.LogError("Output file path is required");
                return false;
            }

            try
            {
                var ffmpegPath = await GetFfmpegPathAsync();
                if (string.IsNullOrEmpty(ffmpegPath))
                {
                    _logger.LogError("FFmpeg executable not found");
                    return false;
                }

                // Verify all input files exist
                var inputFiles = wavFilePaths.ToList();
                foreach (var file in inputFiles)
                {
                    if (!File.Exists(file))
                    {
                        _logger.LogError("Input WAV file not found: {FilePath}", file);
                        return false;
                    }
                }

                // Create output directory if it doesn't exist
                var outputDir = Path.GetDirectoryName(outputFilePath);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                    _logger.LogDebug("Created output directory: {Directory}", outputDir);
                }

                // Build ffmpeg command
                var arguments = BuildFfmpegArguments(inputFiles, outputFilePath, finalAudioFormat, finalAudioBitrate, metadata);
                
                _logger.LogInformation("Starting ffmpeg concatenation and conversion");
                _logger.LogDebug("FFmpeg command: {FfmpegPath} {Arguments}", ffmpegPath, arguments);

                // Execute ffmpeg
                var result = await ExecuteFfmpegAsync(ffmpegPath, arguments);

                if (result.Success)
                {
                    _logger.LogInformation("Successfully concatenated {InputCount} files to {OutputPath}", 
                        inputFiles.Count, outputFilePath);
                    return true;
                }
                else
                {
                    _logger.LogError("FFmpeg failed with exit code {ExitCode}. Error: {Error}", 
                        result.ExitCode, result.ErrorOutput);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during ffmpeg concatenation and conversion");
                return false;
            }
        }

        private async Task<string?> GetFfmpegPathAsync()
        {
            var appSettings = _configurationManager.GetAppSettings();
            
            // First, check if a custom path is configured
            if (!string.IsNullOrWhiteSpace(appSettings.FfmpegPath) && File.Exists(appSettings.FfmpegPath))
            {
                _logger.LogDebug("Using configured ffmpeg path: {Path}", appSettings.FfmpegPath);
                return appSettings.FfmpegPath;
            }

            // Check bundled ffmpeg in 3rdparty folder
            var bundledPath = Path.Combine(AppContext.BaseDirectory, "3rdparty", "ffmpeg.exe");
            if (File.Exists(bundledPath))
            {
                _logger.LogDebug("Using bundled ffmpeg: {Path}", bundledPath);
                return bundledPath;
            }

            // Try to find ffmpeg in PATH
            try
            {
                var pathFfmpeg = "ffmpeg"; // Will use PATH resolution
                var testProcess = new ProcessStartInfo
                {
                    FileName = pathFfmpeg,
                    Arguments = "-version",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(testProcess);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    if (process.ExitCode == 0)
                    {
                        _logger.LogDebug("Using ffmpeg from PATH");
                        return pathFfmpeg;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "FFmpeg not found in PATH");
            }

            _logger.LogError("FFmpeg executable not found. Checked: configured path, bundled (3rdparty/ffmpeg.exe), and PATH");
            return null;
        }

        private string BuildFfmpegArguments(List<string> inputFiles, string outputFilePath, string format, string bitrate, AudioMetadata? metadata = null)
        {
            var appSettings = _configurationManager.GetAppSettings();
            var args = new StringBuilder();

            // Add input files
            foreach (var inputFile in inputFiles)
            {
                args.Append($"-i \"{inputFile}\" ");
            }

            // Build filter complex for concatenation and normalization
            var filterParts = new List<string>();

            // Concatenation filter
            if (inputFiles.Count > 1)
            {
                var concatFilter = new StringBuilder();
                for (int i = 0; i < inputFiles.Count; i++)
                {
                    concatFilter.Append($"[{i}:0]");
                }
                concatFilter.Append($"concat=n={inputFiles.Count}:v=0:a=1[concat]");
                filterParts.Add(concatFilter.ToString());
            }

            // Audio normalization filter (if enabled)
            string audioSource = inputFiles.Count > 1 ? "[concat]" : "[0:0]";
            string finalAudioOutput = "[out]";

            if (appSettings.EnableAudioNormalization)
            {
                var loudnormFilter = $"{audioSource}loudnorm=I={appSettings.NormalizationTargetLufs}:LRA={appSettings.NormalizationLoudnessRange}:TP={appSettings.NormalizationTruePeakLimit}{finalAudioOutput}";
                filterParts.Add(loudnormFilter);
            }
            else if (inputFiles.Count > 1)
            {
                // If no normalization but multiple files, we need to pass through the concat output
                // Use anull filter to create the expected output label
                filterParts.Add($"{audioSource}anull{finalAudioOutput}");
            }

            // Add filter complex if we have filters
            if (filterParts.Any())
            {
                args.Append($"-filter_complex \"{string.Join(";", filterParts)}\" -map \"[out]\" ");
            }
            else if (inputFiles.Count == 1)
            {
                // Single file, no filters needed - map the input directly
                args.Append("-map 0:0 ");
            }

            // Add codec and format-specific settings
            switch (format.ToLowerInvariant())
            {
                case "opus":
                    args.Append($"-c:a libopus -b:a {bitrate} ");

                    // Add Opus-specific optimizations from documentation
                    args.Append($"-ar {appSettings.AudioSampleRate} ");
                    args.Append("-application audio ");
                    args.Append($"-compression_level {appSettings.OpusCompressionLevel} ");

                    if (appSettings.ForceMonoOutput)
                    {
                        args.Append("-ac 1 ");
                    }
                    break;

                case "mp3":
                    args.Append($"-c:a libmp3lame -b:a {bitrate} ");
                    args.Append($"-ar {appSettings.AudioSampleRate} ");

                    if (appSettings.ForceMonoOutput)
                    {
                        args.Append("-ac 1 ");
                    }
                    break;

                case "aac":
                    args.Append($"-c:a aac -b:a {bitrate} ");
                    args.Append($"-ar {appSettings.AudioSampleRate} ");

                    if (appSettings.ForceMonoOutput)
                    {
                        args.Append("-ac 1 ");
                    }
                    break;

                default:
                    _logger.LogWarning("Unknown audio format '{Format}', using default settings", format);
                    args.Append($"-b:a {bitrate} ");
                    args.Append($"-ar {appSettings.AudioSampleRate} ");

                    if (appSettings.ForceMonoOutput)
                    {
                        args.Append("-ac 1 ");
                    }
                    break;
            }

            // Add metadata if provided
            if (metadata != null)
            {
                if (!string.IsNullOrWhiteSpace(metadata.Title))
                    args.Append($"-metadata title=\"{EscapeMetadata(metadata.Title)}\" ");

                if (!string.IsNullOrWhiteSpace(metadata.Artist))
                    args.Append($"-metadata artist=\"{EscapeMetadata(metadata.Artist)}\" ");

                if (!string.IsNullOrWhiteSpace(metadata.AlbumArtist))
                    args.Append($"-metadata album_artist=\"{EscapeMetadata(metadata.AlbumArtist)}\" ");

                if (!string.IsNullOrWhiteSpace(metadata.Album))
                    args.Append($"-metadata album=\"{EscapeMetadata(metadata.Album)}\" ");

                if (metadata.Track.HasValue)
                    args.Append($"-metadata track=\"{metadata.Track.Value}\" ");

                if (!string.IsNullOrWhiteSpace(metadata.Genre))
                    args.Append($"-metadata genre=\"{EscapeMetadata(metadata.Genre)}\" ");

                if (metadata.Year.HasValue)
                    args.Append($"-metadata date=\"{metadata.Year.Value}\" ");

                if (!string.IsNullOrWhiteSpace(metadata.Comment))
                    args.Append($"-metadata comment=\"{EscapeMetadata(metadata.Comment)}\" ");
            }

            // Add output file and overwrite flag
            args.Append($"-y \"{outputFilePath}\"");

            return args.ToString();
        }

        /// <summary>
        /// Escapes metadata values for FFmpeg command line usage
        /// </summary>
        /// <param name="value">The metadata value to escape</param>
        /// <returns>Escaped metadata value</returns>
        private string EscapeMetadata(string value)
        {
            // Escape quotes and backslashes for FFmpeg metadata
            return value.Replace("\\", "\\\\").Replace("\"", "\\\"");
        }

        private async Task<FfmpegResult> ExecuteFfmpegAsync(string ffmpegPath, string arguments)
        {
            var processInfo = new ProcessStartInfo
            {
                FileName = ffmpegPath,
                Arguments = arguments,
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };

            var outputBuilder = new StringBuilder();
            var errorBuilder = new StringBuilder();

            using var process = new Process { StartInfo = processInfo };
            
            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    outputBuilder.AppendLine(e.Data);
                    _logger.LogTrace("FFmpeg stdout: {Output}", e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errorBuilder.AppendLine(e.Data);
                    _logger.LogTrace("FFmpeg stderr: {Error}", e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            await process.WaitForExitAsync();

            return new FfmpegResult
            {
                Success = process.ExitCode == 0,
                ExitCode = process.ExitCode,
                StandardOutput = outputBuilder.ToString(),
                ErrorOutput = errorBuilder.ToString()
            };
        }

        private class FfmpegResult
        {
            public bool Success { get; set; }
            public int ExitCode { get; set; }
            public string StandardOutput { get; set; } = string.Empty;
            public string ErrorOutput { get; set; } = string.Empty;
        }
    }
}
