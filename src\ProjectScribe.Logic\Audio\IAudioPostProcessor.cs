using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectScribe.Logic.Audio
{
    /// <summary>
    /// Interface for audio post-processing operations, including concatenation and format conversion
    /// </summary>
    public interface IAudioPostProcessor
    {
        /// <summary>
        /// Processes a batch of WAV files by concatenating them and converting to the final audio format
        /// </summary>
        /// <param name="wavFilePaths">Collection of WAV file paths to process in order</param>
        /// <param name="bookOutputDirectory">The output directory for the book</param>
        /// <param name="bookName">Name of the book for file naming</param>
        /// <param name="batchNumber">Batch number for file naming (e.g., part001, part002)</param>
        /// <returns>Path to the final audio file if successful, null if failed</returns>
        Task<string?> ProcessBatchAsync(
            IEnumerable<string> wavFilePaths, 
            string bookOutputDirectory, 
            string bookName, 
            int batchNumber);
    }
}
