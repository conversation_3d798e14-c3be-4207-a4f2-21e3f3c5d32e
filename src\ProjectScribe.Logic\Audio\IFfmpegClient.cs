using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectScribe.Logic.Audio
{
    /// <summary>
    /// Interface for FFmpeg client operations
    /// </summary>
    public interface IFfmpegClient
    {
        /// <summary>
        /// Concatenates WAV files and converts them to the specified audio format
        /// </summary>
        /// <param name="wavFilePaths">List of WAV file paths to concatenate in order</param>
        /// <param name="outputFilePath">Path for the output audio file</param>
        /// <param name="finalAudioFormat">Target audio format (e.g., "opus", "mp3")</param>
        /// <param name="finalAudioBitrate">Target bitrate (e.g., "96k", "128k")</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> ConcatenateAndConvertAsync(
            IEnumerable<string> wavFilePaths,
            string outputFilePath,
            string finalAudioFormat,
            string finalAudioBitrate);

        /// <summary>
        /// Concatenates WAV files and converts them to the specified audio format with metadata
        /// </summary>
        /// <param name="wavFilePaths">List of WAV file paths to concatenate in order</param>
        /// <param name="outputFilePath">Path for the output audio file</param>
        /// <param name="finalAudioFormat">Target audio format (e.g., "opus", "mp3")</param>
        /// <param name="finalAudioBitrate">Target bitrate (e.g., "96k", "128k")</param>
        /// <param name="metadata">Optional metadata to embed in the audio file</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> ConcatenateAndConvertAsync(
            IEnumerable<string> wavFilePaths,
            string outputFilePath,
            string finalAudioFormat,
            string finalAudioBitrate,
            AudioMetadata? metadata);
    }
}
