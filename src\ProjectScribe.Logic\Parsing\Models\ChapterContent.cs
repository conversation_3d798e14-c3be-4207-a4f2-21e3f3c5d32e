namespace ProjectScribe.Logic.Parsing.Models
{
    /// <summary>
    /// Represents the content of a single chapter extracted from an input file.
    /// Contains the chapter metadata and textual content for further processing.
    /// </summary>
    public class ChapterContent
    {
        /// <summary>
        /// Sequential number of the chapter within the book (1-based).
        /// Used to maintain proper ordering during processing.
        /// </summary>
        public int ChapterNumber { get; set; }

        /// <summary>
        /// Title or heading of the chapter.
        /// May be empty if the chapter has no explicit title.
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// The extracted textual content of the chapter.
        /// Contains only readable text with HTML markup and non-textual elements removed.
        /// </summary>
        public string TextContent { get; set; } = string.Empty;
    }
}
