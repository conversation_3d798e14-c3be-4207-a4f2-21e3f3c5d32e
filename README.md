# Project Scribe

Project Scribe is a Windows console application designed to automate the conversion of ePub books into high-quality audio files using the Gemini TTS service. Its primary goal is to provide users with audio versions of books, especially those not commercially available in audio format.

## Features

- **High-Quality Gemini TTS Integration**: Leverages the Gemini TTS service for superior audio output
- **Robust and Resilient Processing**: Designed for reliable operation with error handling, retry mechanisms, and resumability
- **Automated Directory Monitoring**: Continuously monitors input directories for new ePub files
- **Seamless Handling of Serialized Content**: Supports incremental addition and processing of book chapters
- **State Management**: SQLite-based state tracking allows resuming processing after interruptions
- **Concurrent Processing**: Configurable multi-threaded TTS processing for improved performance
- **Flexible Audio Output**: Supports multiple output formats (Opus, MP3, AAC) with configurable bitrates

## Prerequisites

- **.NET 9 SDK** or later
- **FFmpeg** (for audio processing)
  - Can be bundled in `3rdparty/ffmpeg.exe`
  - Or available in system PATH
  - Or specified via `FfmpegPath` configuration
- **Gemini TTS API Key** (stored in `.env` file)

## Configuration

### config.json

Create a `config.json` file in the application directory based on `config.json.example`. The configuration file contains the following parameters:

#### Directory Structure
- **`BaseLibraryDirectory`** (string): Base directory for all library operations (default: "d:\\lib")
- **`InputDirectoryName`** (string): Directory name for input ePub files (default: "input")
- **`PrepareDirectoryName`** (string): Directory name for prepared text files (default: "prepare")
- **`WavDirectoryName`** (string): Directory name for WAV audio files (default: "wav")
- **`OutputDirectoryName`** (string): Directory name for final output files (default: "output")

#### File Processing
- **`SupportedInputFileExtensions`** (array): Supported file extensions (default: [".epub"])
- **`TextBlockTargetCharCount`** (int): Target character count for text blocks (default: 1800)

#### TTS Configuration
- **`GeminiTtsApiEndpoint`** (string): Gemini TTS API endpoint URL
- **`GeminiTtsVoiceName`** (string): Voice name for TTS (e.g., "Charon", "Alnilam")
- **`GeminiTtsModel`** (string): TTS model to use (default: "gemini-2.5-flash-preview-tts")
- **`MaxConcurrentTtsThreads`** (int): Maximum concurrent TTS threads (default: 4)
- **`MaxTtsRetries`** (int): Maximum retry attempts for failed TTS requests (default: 3)
- **`TtsRetryInitialDelayMs`** (int): Initial retry delay in milliseconds (default: 5000)
- **`TtsRetryBackoffMultiplier`** (float): Exponential backoff multiplier (default: 2.0)

#### Audio Output
- **`FinalAudioFormat`** (string): Output audio format - "opus", "mp3", or "aac" (default: "opus")
- **`FinalAudioBitrate`** (string): Audio bitrate (default: "96k")
- **`MaxAudioBatchDurationMinutes`** (int): Maximum duration per output file (default: 60)
- **`FfmpegPath`** (string, optional): Custom path to FFmpeg executable

#### System
- **`DefaultLogLevel`** (string): Logging level - "Trace", "Debug", "Information", "Warning", "Error" (default: "Information")
- **`DatabasePath`** (string): Path to SQLite database file (default: "data/project_scribe.db")

### .env File

Create a `.env` file in the application directory based on `.env.example`:

```
GEMINI_API_KEY=your_api_key_here
```

## Directory Structure

The application organizes files in the following structure under `BaseLibraryDirectory`:

```
BaseLibraryDirectory/
├── input/           # Place ePub files here (monitored automatically)
│   └── author/
│       └── series/
│           └── book.epub
├── prepare/         # Processed text files (intermediate)
│   └── author/
│       └── series/
│           └── book/
│               ├── 0001.txt
│               ├── 0002.txt
│               └── ...
├── wav/            # Generated WAV files (intermediate)
│   └── author/
│       └── series/
│           └── book/
│               ├── 0001.wav
│               ├── 0002.wav
│               └── ...
└── output/         # Final audio files
    └── author/
        └── series/
            └── book/
                ├── book_part001.opus
                ├── book_part002.opus
                └── ...
```

## Usage

### Running the Application

```bash
dotnet run --project src/ProjectScribe.Console
```

Or use the build script:

```powershell
.\build.ps1
```

### Processing Books

1. **Place ePub files** in the configured input directory following the `author/series/book.epub` structure
2. **Start the application** - it will automatically detect and process new files
3. **Monitor logs** in the `logs/` directory for processing status
4. **Find output files** in the configured output directory

### Resumability

The application automatically resumes processing from the last completed step if interrupted:
- **Text Processing**: Resumes from chapter parsing if text files are missing
- **TTS Processing**: Continues with pending text blocks
- **Audio Post-Processing**: Processes completed WAV files into final format

## Building from Source

### Prerequisites
- .NET 9 SDK
- Git

### Build Steps

```bash
# Clone the repository
git clone <repository-url>
cd ProjectScribe

# Restore dependencies
dotnet restore

# Build the solution
dotnet build

# Run the application
dotnet run --project src/ProjectScribe.Console
```

## Running Tests

```bash
# Run all tests
dotnet test

# Run tests with detailed output
dotnet test --verbosity normal

# Run specific test project
dotnet test tests/ProjectScribe.Logic.Tests
```

## Logging

The application creates detailed logs in the `logs/` directory:

- **Information-.log**: General operational information
- **Warning-.log**: Warning messages and recoverable errors
- **Error-.log**: Error messages and exceptions
- **Debug-.log**: Detailed debugging information (when enabled)
- **Verbose-.log**: Trace-level debugging information (when enabled)

Log levels are controlled by the `DefaultLogLevel` configuration parameter. Available levels:
- **Trace**: Most detailed logging
- **Debug**: Debugging information
- **Information**: General operational information (default)
- **Warning**: Warning messages only
- **Error**: Error messages only

## Troubleshooting

### Common Issues

**FFmpeg not found**
- Ensure FFmpeg is installed and available in PATH, or
- Place `ffmpeg.exe` in the `3rdparty/` directory, or
- Set the `FfmpegPath` configuration parameter

**TTS API errors**
- Verify your `GEMINI_API_KEY` in the `.env` file
- Check the `GeminiTtsApiEndpoint` configuration
- Review TTS retry settings if experiencing intermittent failures

**Database errors**
- Ensure the `data/` directory is writable
- Check the `DatabasePath` configuration
- Delete the database file to reset state if corrupted

**Processing stuck or failed**
- Check logs in the `logs/` directory for detailed error information
- The application will automatically resume from the last successful step
- Failed books can be reprocessed by restarting the application

### Performance Tuning

- **Adjust `MaxConcurrentTtsThreads`** based on your system capabilities and API limits
- **Modify `TextBlockTargetCharCount`** to balance processing speed and audio file granularity
- **Set appropriate `MaxAudioBatchDurationMinutes`** for desired output file sizes

---

For more information, see the [technical design documentation](docs/technical_design_project_scribe.md) and [directory structure documentation](docs/directory_and_file_structure.md).