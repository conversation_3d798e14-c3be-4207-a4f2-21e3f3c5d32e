using System.Collections.Generic;
namespace ProjectScribe.Logic.Configuration
{
    /// <summary>
    /// Configuration settings for Project Scribe application.
    /// Contains all operational parameters loaded from config.json with sensible defaults.
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// Base directory for all library operations. All other directories are relative to this path.
        /// </summary>
        public string BaseLibraryDirectory { get; set; } = "d:\\lib";

        /// <summary>
        /// Directory name for input files (e.g., ePub files) relative to BaseLibraryDirectory.
        /// </summary>
        public string InputDirectoryName { get; set; } = "input";

        /// <summary>
        /// Directory name for prepared text files relative to BaseLibraryDirectory.
        /// </summary>
        public string PrepareDirectoryName { get; set; } = "prepare";

        /// <summary>
        /// Directory name for style-enhanced text files (relative to book directory).
        /// </summary>
        public string StyledDirectoryName { get; set; } = "styled";

        /// <summary>
        /// Directory name for WAV audio files relative to BaseLibraryDirectory.
        /// </summary>
        public string WavDirectoryName { get; set; } = "wav";

        /// <summary>
        /// Directory name for final output audio files relative to BaseLibraryDirectory.
        /// </summary>
        public string OutputDirectoryName { get; set; } = "output";
        /// <summary>
        /// List of supported input file extensions (e.g., ".epub", ".pdf").
        /// </summary>
        public List<string> SupportedInputFileExtensions { get; set; } = new List<string> { ".epub" }; // Default with .epub

        /// <summary>
        /// API endpoint URL for the Gemini TTS service.
        /// </summary>
        public string GeminiTtsApiEndpoint { get; set; } = "https://gemini-openai-adapter.100169.xyz/tts";

        /// <summary>
        /// First voice name to use for TTS generation (e.g., "Charon", "Alnilam").
        /// Used for narration and male characters.
        /// </summary>
        public string GeminiTtsFirstVoiceName { get; set; } = "Charon";

        /// <summary>
        /// Second voice name to use for TTS generation (e.g., "Zephyr", "Puck").
        /// Used for female characters.
        /// </summary>
        public string GeminiTtsSecondVoiceName { get; set; } = "Zephyr";

        /// <summary>
        /// TTS model to use for generation (e.g., "gemini-2.5-flash-preview-tts").
        /// </summary>
        public string GeminiTtsModel { get; set; } = "gemini-2.5-flash-preview-tts";

        /// <summary>
        /// Target character count for text blocks before splitting.
        /// </summary>
        public int TextBlockTargetCharCount { get; set; } = 1800;

        /// <summary>
        /// Style preset to prepend to each text block for TTS processing.
        /// Contains narrator instructions and voice guidance.
        /// </summary>
        public string StylePreset { get; set; } = @"STYLE:
You are Travis Baldree, professional audiobook narrator. Your task is to narrate provided text.
For narration and male characters, use your signature clear, engaging masculine voice (speaker 1 / first voice).
For female characters, use a distinctly different, feminine-sounding voice, ensuring it is clearly distinguishable yet natural, in your characteristic style (speaker 2 / second voice).
Crucially, give each individual character, regardless of gender, their own unique vocal intonation, emotion, and delivery style to make them memorable and distinct.
Prepare to receive text for narration.
TEXT:

";

        /// <summary>
        /// Maximum number of concurrent TTS processing threads.
        /// </summary>
        public int MaxConcurrentTtsThreads { get; set; } = 4;

        /// <summary>
        /// Maximum number of retry attempts for failed TTS requests.
        /// </summary>
        public int MaxTtsRetries { get; set; } = 3;

        /// <summary>
        /// Initial delay in milliseconds before the first TTS retry attempt.
        /// Used as the base delay for exponential backoff calculations.
        /// Default: 30000ms (30 seconds).
        /// </summary>
        public int TtsRetryInitialDelayMs { get; set; } = 30000;

        /// <summary>
        /// Multiplier for exponential backoff between TTS retry attempts.
        /// Each subsequent retry delay is calculated as: InitialDelay * Multiplier^(attemptNumber - 1).
        /// Default: 2.0 (doubles the delay each time).
        /// </summary>
        public float TtsRetryBackoffMultiplier { get; set; } = 2.0f;
        /// <summary>
        /// Final audio format for output files (e.g., "opus", "mp3", "aac").
        /// </summary>
        public string FinalAudioFormat { get; set; } = "opus";

        /// <summary>
        /// Bitrate for final audio files (e.g., "96k", "128k").
        /// </summary>
        public string FinalAudioBitrate { get; set; } = "128k";

        /// <summary>
        /// Opus compression level (0-10). Higher values mean slower encoding but better quality.
        /// Default is 10 for best quality.
        /// </summary>
        public int OpusCompressionLevel { get; set; } = 10;

        /// <summary>
        /// Target sample rate for audio output. 48kHz is optimal for Opus.
        /// </summary>
        public int AudioSampleRate { get; set; } = 48000;

        /// <summary>
        /// Force mono output for audiobooks (saves space, audiobooks are typically mono).
        /// </summary>
        public bool ForceMonoOutput { get; set; } = true;

        /// <summary>
        /// Enable audio normalization using loudnorm filter for consistent volume levels.
        /// </summary>
        public bool EnableAudioNormalization { get; set; } = true;

        /// <summary>
        /// Target integrated loudness in LUFS for audio normalization. -18 LUFS is good for spoken word.
        /// </summary>
        public double NormalizationTargetLufs { get; set; } = -18.0;

        /// <summary>
        /// Target loudness range for audio normalization.
        /// </summary>
        public double NormalizationLoudnessRange { get; set; } = 7.0;

        /// <summary>
        /// True peak limit in dBFS for audio normalization.
        /// </summary>
        public double NormalizationTruePeakLimit { get; set; } = -1.5;

        /// <summary>
        /// Maximum duration in minutes for each audio batch before creating a new file.
        /// </summary>
        public int MaxAudioBatchDurationMinutes { get; set; } = 60;

        /// <summary>
        /// Optional custom path to FFmpeg executable. If null or empty, will search in bundled location and PATH.
        /// </summary>
        public string? FfmpegPath { get; set; }

        /// <summary>
        /// Default log level for the application (e.g., "Information", "Debug", "Warning").
        /// </summary>
        public string DefaultLogLevel { get; set; } = "Information";

        /// <summary>
        /// Path to the SQLite database file for state management.
        /// </summary>
        public string DatabasePath { get; set; } = "data/project_scribe.db";
    }
}