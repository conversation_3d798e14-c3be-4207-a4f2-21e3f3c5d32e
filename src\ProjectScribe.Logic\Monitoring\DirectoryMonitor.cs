using System;
using System.IO;
using System.Linq;
using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;

namespace ProjectScribe.Logic.Monitoring
{
    /// <summary>
    /// Monitors a directory for new input files using FileSystemWatcher.
    /// Raises events when new supported files are detected.
    /// </summary>
    public class DirectoryMonitor : IDirectoryMonitor
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly ILogger<DirectoryMonitor> _logger;
        private FileSystemWatcher? _fileSystemWatcher;
        private string? _monitoredPath;
        private bool _disposed = false;

        public DirectoryMonitor(IConfigurationManager configurationManager, ILogger<DirectoryMonitor> logger)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public event EventHandler<string>? NewFileDetected;

        public bool IsMonitoring => _fileSystemWatcher?.EnableRaisingEvents == true;

        public void StartMonitoring()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(DirectoryMonitor));
            }

            if (IsMonitoring)
            {
                _logger.LogWarning("Directory monitoring is already active");
                return;
            }

            try
            {
                var appSettings = _configurationManager.GetAppSettings();
                _monitoredPath = Path.Combine(appSettings.BaseLibraryDirectory, appSettings.InputDirectoryName);

                // Ensure the directory exists
                if (!Directory.Exists(_monitoredPath))
                {
                    _logger.LogWarning("Input directory does not exist: {Path}. Creating directory.", _monitoredPath);
                    Directory.CreateDirectory(_monitoredPath);
                }

                // Validate supported extensions
                if (appSettings.SupportedInputFileExtensions == null || !appSettings.SupportedInputFileExtensions.Any())
                {
                    _logger.LogError("No supported input file extensions configured. Cannot start monitoring.");
                    return;
                }

                // Create and configure FileSystemWatcher
                _fileSystemWatcher = new FileSystemWatcher(_monitoredPath)
                {
                    IncludeSubdirectories = true,
                    NotifyFilter = NotifyFilters.FileName | NotifyFilters.CreationTime,
                    EnableRaisingEvents = false // Will be enabled after event subscription
                };

                // Subscribe to events
                _fileSystemWatcher.Created += OnFileCreated;
                _fileSystemWatcher.Error += OnError;

                // Start monitoring
                _fileSystemWatcher.EnableRaisingEvents = true;

                _logger.LogInformation("Started monitoring directory: {Path} for extensions: {Extensions}", 
                    _monitoredPath, string.Join(", ", appSettings.SupportedInputFileExtensions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start directory monitoring for path: {Path}", _monitoredPath);
                StopMonitoring();
                throw;
            }
        }

        public void StopMonitoring()
        {
            if (_fileSystemWatcher != null)
            {
                try
                {
                    _fileSystemWatcher.EnableRaisingEvents = false;
                    _fileSystemWatcher.Created -= OnFileCreated;
                    _fileSystemWatcher.Error -= OnError;
                    _fileSystemWatcher.Dispose();
                    _fileSystemWatcher = null;

                    _logger.LogInformation("Stopped monitoring directory: {Path}", _monitoredPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while stopping directory monitoring");
                }
            }
        }

        private void OnFileCreated(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(e.FullPath))
                {
                    return;
                }

                // Check if the file has a supported extension
                var fileExtension = Path.GetExtension(e.FullPath);
                var appSettings = _configurationManager.GetAppSettings();
                
                if (appSettings.SupportedInputFileExtensions?.Contains(fileExtension, StringComparer.OrdinalIgnoreCase) == true)
                {
                    _logger.LogInformation("New supported file detected: {FilePath}", e.FullPath);
                    
                    // Raise the event in a thread-safe manner
                    NewFileDetected?.Invoke(this, e.FullPath);
                }
                else
                {
                    _logger.LogDebug("File detected but not supported: {FilePath} (extension: {Extension})", 
                        e.FullPath, fileExtension);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing file creation event for: {FilePath}", e.FullPath);
            }
        }

        private void OnError(object sender, ErrorEventArgs e)
        {
            _logger.LogError(e.GetException(), "FileSystemWatcher error occurred. Monitoring may have stopped.");
            
            // Attempt to restart monitoring after an error
            try
            {
                StopMonitoring();
                _logger.LogInformation("Attempting to restart directory monitoring after error...");
                StartMonitoring();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to restart directory monitoring after error");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _disposed = true;
            }
        }
    }
}
