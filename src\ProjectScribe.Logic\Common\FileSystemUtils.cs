using System;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace ProjectScribe.Logic.Common
{
    /// <summary>
    /// Utility class for safe file system operations and path management.
    /// Provides methods for creating directories and sanitizing file names.
    /// </summary>
    public static class FileSystemUtils
    {
        /// <summary>
        /// Creates a directory structure if it doesn't exist, with proper error handling.
        /// </summary>
        /// <param name="directoryPath">The full path to the directory to create.</param>
        /// <exception cref="ArgumentException">Thrown when the directory path is null or empty.</exception>
        /// <exception cref="UnauthorizedAccessException">Thrown when access to create the directory is denied.</exception>
        /// <exception cref="DirectoryNotFoundException">Thrown when the parent directory doesn't exist and can't be created.</exception>
        public static void EnsureDirectoryExists(string directoryPath)
        {
            if (string.IsNullOrWhiteSpace(directoryPath))
                throw new ArgumentException("Directory path cannot be null or empty.", nameof(directoryPath));

            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        /// <summary>
        /// Safely combines path segments and ensures the resulting directory exists.
        /// </summary>
        /// <param name="basePath">The base directory path.</param>
        /// <param name="segments">Additional path segments to combine.</param>
        /// <returns>The combined path with all directories created.</returns>
        public static string CombineAndEnsurePath(string basePath, params string[] segments)
        {
            if (string.IsNullOrWhiteSpace(basePath))
                throw new ArgumentException("Base path cannot be null or empty.", nameof(basePath));

            var combinedPath = basePath;
            foreach (var segment in segments)
            {
                if (!string.IsNullOrWhiteSpace(segment))
                {
                    combinedPath = Path.Combine(combinedPath, SanitizePathSegment(segment));
                }
            }

            EnsureDirectoryExists(combinedPath);
            return combinedPath;
        }

        /// <summary>
        /// Sanitizes a path segment by removing or replacing invalid characters.
        /// </summary>
        /// <param name="pathSegment">The path segment to sanitize.</param>
        /// <returns>A sanitized path segment safe for use in file system operations.</returns>
        public static string SanitizePathSegment(string pathSegment)
        {
            if (string.IsNullOrWhiteSpace(pathSegment))
                return "unknown";

            // Remove invalid path characters
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = pathSegment;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Replace multiple consecutive spaces/underscores with single underscore
            sanitized = Regex.Replace(sanitized, @"[\s_]+", "_");

            // Remove leading/trailing underscores and dots
            sanitized = sanitized.Trim('_', '.', ' ');

            // Ensure we don't have an empty string
            if (string.IsNullOrWhiteSpace(sanitized))
                sanitized = "unknown";

            // Limit length to reasonable size
            if (sanitized.Length > 100)
                sanitized = sanitized.Substring(0, 100).TrimEnd('_', '.', ' ');

            return sanitized;
        }

        /// <summary>
        /// Generates a unique book ID from a file path using a deterministic hash-based approach.
        /// Uses SHA256 to ensure the same file path always generates the same book ID.
        /// </summary>
        /// <param name="filePath">The full path to the book file.</param>
        /// <returns>A unique book identifier.</returns>
        public static string GenerateBookId(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be null or empty.", nameof(filePath));

            // Use the relative path or filename for consistency
            var fileName = Path.GetFileNameWithoutExtension(filePath);

            // Generate deterministic hash using SHA256
            var hash = GenerateDeterministicHash(filePath);

            return $"{SanitizePathSegment(fileName)}_{hash:X8}";
        }

        /// <summary>
        /// Generates a deterministic hash from a string using SHA256.
        /// Unlike string.GetHashCode(), this will always return the same value for the same input.
        /// </summary>
        /// <param name="input">The input string to hash.</param>
        /// <returns>A deterministic 32-bit hash value.</returns>
        private static uint GenerateDeterministicHash(string input)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));

                // Take the first 4 bytes and convert to uint
                return BitConverter.ToUInt32(hashBytes, 0);
            }
        }

        /// <summary>
        /// Extracts directory structure components from a file path for organizing output.
        /// Attempts to determine author, series, and book name from the path structure.
        /// </summary>
        /// <param name="filePath">The full path to the book file.</param>
        /// <returns>A tuple containing (author, series, bookName) components.</returns>
        public static (string author, string series, string bookName) ExtractPathComponents(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return ("unknown_author", "unknown_series", "unknown_book");

            var pathParts = filePath.Split(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar)
                .Where(part => !string.IsNullOrWhiteSpace(part))
                .ToArray();

            var bookName = Path.GetFileNameWithoutExtension(filePath);
            
            // Try to extract author and series from path structure
            // Assuming structure like: .../author/series/book.epub or .../author/book.epub
            string author = "unknown_author";
            string series = "unknown_series";

            if (pathParts.Length >= 2)
            {
                author = pathParts[pathParts.Length - 2]; // Parent directory
                
                if (pathParts.Length >= 3)
                {
                    // If we have grandparent, use it as author and parent as series
                    author = pathParts[pathParts.Length - 3];
                    series = pathParts[pathParts.Length - 2];
                }
            }

            return (
                SanitizePathSegment(author),
                SanitizePathSegment(series),
                SanitizePathSegment(bookName)
            );
        }
    }
}
