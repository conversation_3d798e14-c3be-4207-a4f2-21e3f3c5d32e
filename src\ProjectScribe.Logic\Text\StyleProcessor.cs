using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.State.Entities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectScribe.Logic.Text
{
    /// <summary>
    /// Implementation of style processing services for enhancing text blocks with style presets.
    /// <PERSON><PERSON> adding narrator instructions and style guidance to text blocks before TTS processing.
    /// </summary>
    public class StyleProcessor : IStyleProcessor
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly ILogger<StyleProcessor> _logger;

        /// <summary>
        /// Initializes a new instance of the StyleProcessor class.
        /// </summary>
        /// <param name="configurationManager">Configuration manager for accessing app settings.</param>
        /// <param name="logger">Logger for recording processing activities.</param>
        /// <exception cref="ArgumentNullException">Thrown when configurationManager or logger is null.</exception>
        public StyleProcessor(IConfigurationManager configurationManager, ILogger<StyleProcessor> logger)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Processes text blocks by adding style presets and saves them to styled text files.
        /// </summary>
        /// <param name="textBlocks">Collection of text blocks to enhance with style presets.</param>
        /// <param name="bookId">The unique identifier of the book these text blocks belong to.</param>
        /// <param name="styledDir">Directory path where styled text files should be saved.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        /// <exception cref="ArgumentNullException">Thrown when textBlocks, bookId, or styledDir is null.</exception>
        /// <exception cref="ArgumentException">Thrown when bookId or styledDir is empty or whitespace.</exception>
        public async Task ProcessTextBlocksAsync(IEnumerable<TextBlockState> textBlocks, string bookId, string styledDir)
        {
            if (textBlocks == null)
                throw new ArgumentNullException(nameof(textBlocks));
            if (string.IsNullOrWhiteSpace(bookId))
                throw new ArgumentException("Book ID cannot be null or empty.", nameof(bookId));
            if (string.IsNullOrWhiteSpace(styledDir))
                throw new ArgumentException("Styled directory path cannot be null or empty.", nameof(styledDir));

            var appSettings = _configurationManager.GetAppSettings();
            var stylePreset = appSettings.StylePreset;

            _logger.LogDebug("Starting style enhancement for {BlockCount} text blocks in book {BookId}", 
                textBlocks.Count(), bookId);

            try
            {
                // Ensure styled directory exists
                Directory.CreateDirectory(styledDir);

                foreach (var textBlock in textBlocks)
                {
                    await ProcessSingleTextBlockAsync(textBlock, stylePreset, styledDir);
                }

                _logger.LogInformation("Successfully completed style enhancement for {BlockCount} text blocks in book {BookId}",
                    textBlocks.Count(), bookId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process style enhancement for book {BookId}", bookId);
                throw new InvalidOperationException($"Failed to process style enhancement: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Processes a single text block by adding style preset and saving to styled directory.
        /// </summary>
        /// <param name="textBlock">The text block to process.</param>
        /// <param name="stylePreset">The style preset to prepend to the text.</param>
        /// <param name="styledDir">Directory path where styled text file should be saved.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        private async Task ProcessSingleTextBlockAsync(TextBlockState textBlock, string stylePreset, string styledDir)
        {
            try
            {
                _logger.LogTrace("Processing style enhancement for text block {TextBlockId}", textBlock.TextBlockId);

                // Read the original sanitized text
                if (!File.Exists(textBlock.TextFilePath))
                {
                    throw new FileNotFoundException($"Text file not found: {textBlock.TextFilePath}");
                }

                var originalText = await File.ReadAllTextAsync(textBlock.TextFilePath);
                _logger.LogTrace("Read {CharCount} characters from {FilePath}", 
                    originalText.Length, textBlock.TextFilePath);

                // Combine style preset with original text
                var styledText = stylePreset + originalText;

                // Generate styled file path
                var originalFileName = Path.GetFileName(textBlock.TextFilePath);
                var styledFilePath = Path.Combine(styledDir, originalFileName);

                // Save styled text to file
                await File.WriteAllTextAsync(styledFilePath, styledText);

                _logger.LogDebug("Created styled text file for block {BlockSequence}: {FilePath} ({CharCount} chars)",
                    textBlock.BlockSequenceNumber, styledFilePath, styledText.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process style enhancement for text block {TextBlockId}", textBlock.TextBlockId);
                throw;
            }
        }
    }
}
