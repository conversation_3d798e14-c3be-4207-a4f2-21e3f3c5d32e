using System;

namespace ProjectScribe.Logic.Monitoring
{
    /// <summary>
    /// Interface for monitoring directories for new input files.
    /// Provides events when new supported files are detected in the configured input directory.
    /// </summary>
    public interface IDirectoryMonitor : IDisposable
    {
        /// <summary>
        /// Event raised when a new supported file is detected in the monitored directory.
        /// The string parameter contains the full path to the detected file.
        /// </summary>
        event EventHandler<string> NewFileDetected;

        /// <summary>
        /// Starts monitoring the configured input directory for new files.
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// Stops monitoring the directory.
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// Gets a value indicating whether the monitor is currently active.
        /// </summary>
        bool IsMonitoring { get; }
    }
}
