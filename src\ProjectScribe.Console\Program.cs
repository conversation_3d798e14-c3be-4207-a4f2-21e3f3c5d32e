﻿using Microsoft.Extensions.Logging;
using ProjectScribe.Console.Services;
using DotNetEnv;

namespace ProjectScribe.Console
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Load .env file for API keys and other environment variables
            LoadEnvironmentFile();

            // Create and run the application
            using var applicationHost = new ApplicationHost();
            try
            {
                await applicationHost.RunAsync();
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"Fatal error: {ex.Message}");
                Environment.Exit(1);
            }
        }

        private static void LoadEnvironmentFile()
        {
            try
            {
                var envPath = Path.Combine(AppContext.BaseDirectory, ".env");
                if (File.Exists(envPath))
                {
                    Env.Load(envPath);
                    System.Console.WriteLine("Loaded .env file successfully.");
                }
                else
                {
                    System.Console.WriteLine($"Warning: .env file not found at {envPath}");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"Error loading .env file: {ex.Message}");
            }
        }
    }
}
