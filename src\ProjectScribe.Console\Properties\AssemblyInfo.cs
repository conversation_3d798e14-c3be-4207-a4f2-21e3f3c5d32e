using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// General Information about an assembly is controlled through the following
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
[assembly: AssemblyTitle("ProjectScribe.Console")]
[assembly: AssemblyDescription("Simple console app for ProjectScribe - an automated audiobook creation system")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("ProjectScribe")]
[assembly: AssemblyProduct("ProjectScribe")]
[assembly: AssemblyCopyright("Copyright © ProjectScribe 2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// Setting ComVisible to false makes the types in this assembly not visible
// to COM components.  If you need to access a type in this assembly from
// COM, set the ComVisible attribute to true on that type.
[assembly: ComVisible(false)]

// The following GUID is for the ID of the typelib if this project is exposed to COM
[assembly: Guid("a1b2c3d4-0002-7890-abcd-ef1234567890")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("*******")]

// Make internals visible to test assembly
[assembly: InternalsVisibleTo("ProjectScribe.Console.Tests")]
