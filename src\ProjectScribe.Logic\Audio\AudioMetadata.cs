namespace ProjectScribe.Logic.Audio
{
    /// <summary>
    /// Represents metadata to be embedded in audio files
    /// </summary>
    public class AudioMetadata
    {
        /// <summary>
        /// Title of the audio track (e.g., "Book Title - Chapter 1" or "Book Title - Part 001")
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Artist/Author name
        /// </summary>
        public string? Artist { get; set; }

        /// <summary>
        /// Album artist (typically the narrator for audiobooks)
        /// </summary>
        public string? AlbumArtist { get; set; }

        /// <summary>
        /// Album name (typically the book title)
        /// </summary>
        public string? Album { get; set; }

        /// <summary>
        /// Track number (for multi-part audiobooks)
        /// </summary>
        public int? Track { get; set; }

        /// <summary>
        /// Genre (typically "Audiobook")
        /// </summary>
        public string? Genre { get; set; } = "Audiobook";

        /// <summary>
        /// Year of publication
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// Comment or description
        /// </summary>
        public string? Comment { get; set; }
    }
}
