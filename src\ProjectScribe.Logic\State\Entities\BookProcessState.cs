using System.ComponentModel.DataAnnotations;

namespace ProjectScribe.Logic.State.Entities
{
    /// <summary>
    /// Represents the processing state of a book in the system.
    /// Tracks overall progress and status of book conversion from ePub to audiobook.
    /// </summary>
    public class BookProcessState
    {
        /// <summary>
        /// Unique identifier for the book, typically derived from file path or filename hash.
        /// </summary>
        [Key]
        public string BookId { get; set; } = string.Empty;

        /// <summary>
        /// Original file path of the input ePub file.
        /// </summary>
        [Required]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// Current processing status of the book.
        /// </summary>
        [Required]
        public BookProcessStatus Status { get; set; } = BookProcessStatus.Pending;

        /// <summary>
        /// Detailed description of the last successfully completed processing step.
        /// Used for resumability to determine where to continue processing.
        /// </summary>
        public string? LastProcessedStep { get; set; }

        /// <summary>
        /// Error message if the book processing failed.
        /// Null if no errors occurred or processing is still in progress.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Timestamp when the book was first added to the system.
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when the book state was last updated.
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for related text blocks.
        /// </summary>
        public virtual ICollection<TextBlockState> TextBlocks { get; set; } = new List<TextBlockState>();
    }

    /// <summary>
    /// Enumeration of possible book processing statuses.
    /// </summary>
    public enum BookProcessStatus
    {
        /// <summary>
        /// Book has been detected but processing has not started.
        /// </summary>
        Pending = 0,

        /// <summary>
        /// Currently parsing the ePub file and extracting chapters.
        /// </summary>
        ProcessingText = 1,

        /// <summary>
        /// Text processing completed, currently enhancing text with style presets.
        /// </summary>
        ProcessingStyleEnhancement = 2,

        /// <summary>
        /// Style enhancement completed, ready for or currently processing TTS.
        /// </summary>
        ProcessingAudio = 3,

        /// <summary>
        /// All processing completed successfully.
        /// </summary>
        Completed = 4,

        /// <summary>
        /// Processing failed and cannot be resumed automatically.
        /// </summary>
        Failed = 5,

        /// <summary>
        /// Processing was paused due to recoverable errors.
        /// Can be resumed after addressing the issues.
        /// </summary>
        Paused = 6
    }
}
