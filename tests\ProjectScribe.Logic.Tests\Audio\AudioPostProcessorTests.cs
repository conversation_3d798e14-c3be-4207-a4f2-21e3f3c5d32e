using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Audio;
using ProjectScribe.Logic.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Xunit;

namespace ProjectScribe.Logic.Tests.Audio
{
    public class AudioPostProcessorTests : IDisposable
    {
        private readonly Mock<IConfigurationManager> _mockConfigurationManager;
        private readonly Mock<IFfmpegClient> _mockFfmpegClient;
        private readonly Mock<ILogger<AudioPostProcessor>> _mockLogger;
        private readonly AudioPostProcessor _audioPostProcessor;
        private readonly string _testDirectory;

        public AudioPostProcessorTests()
        {
            _mockConfigurationManager = new Mock<IConfigurationManager>();
            _mockFfmpegClient = new Mock<IFfmpegClient>();
            _mockLogger = new Mock<ILogger<AudioPostProcessor>>();
            
            _audioPostProcessor = new AudioPostProcessor(
                _mockConfigurationManager.Object,
                _mockFfmpegClient.Object,
                _mockLogger.Object);
            
            // Create a temporary test directory
            _testDirectory = Path.Combine(Path.GetTempPath(), "ProjectScribeTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
        }

        public void Dispose()
        {
            // Clean up test directory
            try
            {
                if (Directory.Exists(_testDirectory))
                {
                    Directory.Delete(_testDirectory, true);
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        [Fact]
        public async Task ProcessBatchAsync_WithNullWavFilePaths_ReturnsNull()
        {
            // Arrange
            SetupMockConfiguration();

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                null!,
                _testDirectory,
                "TestBook",
                1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ProcessBatchAsync_WithEmptyWavFilePaths_ReturnsNull()
        {
            // Arrange
            SetupMockConfiguration();

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                new List<string>(), 
                _testDirectory, 
                "TestBook", 
                1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ProcessBatchAsync_WithNullBookOutputDirectory_ReturnsNull()
        {
            // Arrange
            SetupMockConfiguration();
            var wavFiles = CreateTestWavFiles(2);

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                wavFiles,
                null!,
                "TestBook",
                1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ProcessBatchAsync_WithNullBookName_ReturnsNull()
        {
            // Arrange
            SetupMockConfiguration();
            var wavFiles = CreateTestWavFiles(2);

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                wavFiles,
                _testDirectory,
                null!,
                1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ProcessBatchAsync_WithNonExistentWavFile_ReturnsNull()
        {
            // Arrange
            SetupMockConfiguration();
            var wavFiles = new List<string> { "nonexistent.wav" };

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                wavFiles, 
                _testDirectory, 
                "TestBook", 
                1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ProcessBatchAsync_WithValidInputsAndSuccessfulFfmpeg_ReturnsOutputPath()
        {
            // Arrange
            SetupMockConfiguration();
            var wavFiles = CreateTestWavFiles(3);
            var outputDirectory = Path.Combine(_testDirectory, "output");
            
            // Setup ffmpeg mock to return success
            _mockFfmpegClient
                .Setup(x => x.ConcatenateAndConvertAsync(
                    It.IsAny<IEnumerable<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<AudioMetadata>()))
                .Returns(Task.FromResult(true))
                .Callback<IEnumerable<string>, string, string, string, AudioMetadata>((inputs, output, format, bitrate, metadata) =>
                {
                    // Create the output file to simulate successful ffmpeg execution
                    Directory.CreateDirectory(Path.GetDirectoryName(output)!);
                    File.WriteAllText(output, "fake audio content");
                });

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                wavFiles, 
                outputDirectory, 
                "TestBook", 
                1);

            // Assert
            Assert.NotNull(result);
            Assert.Contains("TestBook_part001.opus", result);
            Assert.True(File.Exists(result));
        }

        [Fact]
        public async Task ProcessBatchAsync_WithFailedFfmpeg_ReturnsNull()
        {
            // Arrange
            SetupMockConfiguration();
            var wavFiles = CreateTestWavFiles(2);
            var outputDirectory = Path.Combine(_testDirectory, "output");
            
            // Setup ffmpeg mock to return failure
            _mockFfmpegClient
                .Setup(x => x.ConcatenateAndConvertAsync(
                    It.IsAny<IEnumerable<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<AudioMetadata>()))
                .Returns(Task.FromResult(false));

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                wavFiles, 
                outputDirectory, 
                "TestBook", 
                1);

            // Assert
            Assert.Null(result);
        }

        [Theory]
        [InlineData("TestBook", 1, "opus", "TestBook_part001.opus")]
        [InlineData("My Great Book", 5, "mp3", "My_Great_Book_part005.mp3")]
        [InlineData("Book/With\\Invalid:Chars", 10, "aac", "Book_With_Invalid_Chars_part010.aac")]
        public async Task ProcessBatchAsync_GeneratesCorrectFileName(string bookName, int batchNumber, string format, string expectedFileName)
        {
            // Arrange
            SetupMockConfiguration(format);
            var wavFiles = CreateTestWavFiles(1);
            var outputDirectory = Path.Combine(_testDirectory, "output");
            
            string? actualOutputPath = null;
            
            // Setup ffmpeg mock to capture the output path
            _mockFfmpegClient
                .Setup(x => x.ConcatenateAndConvertAsync(
                    It.IsAny<IEnumerable<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<AudioMetadata>()))
                .Returns(Task.FromResult(true))
                .Callback<IEnumerable<string>, string, string, string, AudioMetadata>((inputs, output, fmt, bitrate, metadata) =>
                {
                    actualOutputPath = output;
                    Directory.CreateDirectory(Path.GetDirectoryName(output)!);
                    File.WriteAllText(output, "fake audio content");
                });

            // Act
            var result = await _audioPostProcessor.ProcessBatchAsync(
                wavFiles, 
                outputDirectory, 
                bookName, 
                batchNumber);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(actualOutputPath);
            var actualFileName = Path.GetFileName(actualOutputPath);
            Assert.Equal(expectedFileName, actualFileName);
        }

        private void SetupMockConfiguration(string format = "opus", string bitrate = "128k")
        {
            var appSettings = new AppSettings
            {
                FinalAudioFormat = format,
                FinalAudioBitrate = bitrate,
                OutputDirectoryName = "output"
            };

            _mockConfigurationManager
                .Setup(x => x.GetAppSettings())
                .Returns(appSettings);
        }

        private List<string> CreateTestWavFiles(int count)
        {
            var files = new List<string>();
            
            for (int i = 0; i < count; i++)
            {
                var fileName = Path.Combine(_testDirectory, $"test_{i:D3}.wav");
                
                // Create a minimal WAV file
                CreateMinimalWavFile(fileName);
                files.Add(fileName);
            }
            
            return files;
        }

        private void CreateMinimalWavFile(string filePath)
        {
            // Create a minimal WAV file with just the header
            using var fs = new FileStream(filePath, FileMode.Create);
            using var writer = new BinaryWriter(fs);
            
            // WAV header (44 bytes)
            writer.Write("RIFF".ToCharArray());
            writer.Write(36); // File size - 8
            writer.Write("WAVE".ToCharArray());
            writer.Write("fmt ".ToCharArray());
            writer.Write(16); // Subchunk1Size
            writer.Write((short)1); // AudioFormat (PCM)
            writer.Write((short)1); // NumChannels (mono)
            writer.Write(44100); // SampleRate
            writer.Write(88200); // ByteRate
            writer.Write((short)2); // BlockAlign
            writer.Write((short)16); // BitsPerSample
            writer.Write("data".ToCharArray());
            writer.Write(0); // Subchunk2Size (no actual data)
        }
    }
}
