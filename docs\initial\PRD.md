**Product Requirements Document: Project Scribe**

**Version:** 1.0
**Date:** May 31, 2025
**Author/Owner:** vadash

**Table of Contents**
1.  Product Overview
2.  Goals and Objectives
3.  Target User and User Persona
4.  Functional Requirements
    4.1. Configuration Management
    4.2. Directory Monitoring and Input File Ingestion
    4.3. Book Content Parsing and Chapter Extraction
    4.4. Text Processing and Splitting
    4.5. TTS Interaction (Gemini API)
    4.6. Audio Post-Processing (Final Format Conversion)
    4.7. State Management and Resumability
    4.8. Logging and Notifications
    4.9. Modularity for Future Enhancements
5.  Non-Functional Requirements (NFRs)
    5.1. Performance
    5.2. Reliability & Resilience
    5.3. Maintainability
    5.4. Testability
    5.5. Usability
    5.6. Extensibility
6.  Development and Technical Considerations
    6.1. Solution Structure
    6.2. Key Technologies & Libraries
    6.3. TTS API Interaction Details (Gemini)
    6.4. Development Principles
    6.5. Configuration Parameters Summary
7.  Out of Scope (For Initial Version)
8.  Open Questions (For Future Consideration)

---

**1. Product Overview**

*   **1.1. Product Name:** Project Scribe
*   **1.2. Product Statement:** Project Scribe is a Windows console application designed to automate the conversion of ePub books (and potentially other formats in the future) into high-quality audio files using the Gemini TTS service. Its primary goal is to provide users with audio versions of books, especially those not commercially available in audio format.
*   **1.3. Target Audience:** The application is intended for technically savvy individuals for personal use. While it operates as a console application, ongoing use is designed to be fully automated through directory monitoring, requiring user interaction primarily for initial setup and configuration file management.
*   **1.4. Key Benefits & Unique Value Proposition:**
    *   **High-Quality Gemini TTS Integration:** Leverages the Gemini TTS service for superior audio output.
    *   **Robust and Resilient Processing:** Designed for reliable operation, including robust error handling, retry mechanisms for TTS requests, and the ability to resume processing after interruptions.
    *   **Seamless Handling of Serialized Content:** Supports the incremental addition and processing of book chapters or parts (e.g., from web serials), ensuring consistent voice and output for ongoing stories.
    *   **Future Extensibility:** Architected to allow for future enhancements, such as advanced text preprocessing or support for additional input formats.

---

**2. Goals and Objectives**

*   **2.1. User Goals:**
    *   **UG1: "Set It and Forget It" Audiobook Creation:** Users want a highly automated system to convert their ePub book library into audio files using the Gemini TTS service. Once configured, the application should operate autonomously by monitoring input directories, requiring minimal ongoing user interaction.
    *   **UG2: Portable Audio Content:** Users want to easily generate standardized audio files (e.g., Opus or MP3 format) on their PC that can be seamlessly transferred to and played on their mobile devices for convenient, on-the-go listening.

*   **2.2. Project Goals:**
    *   **PG1: Robust and Comprehensive Automated Conversion:**
        *   The application must reliably convert supported input files (initially ePubs) into organized audio files, successfully handling complexities such as:
            *   Books with a very large number of chapters (e.g., 100+).
            *   Non-standard ePub formatting (within reasonable limits, focusing on text extraction).
            *   Special characters requiring sanitization beyond basic Unicode readability.
            *   Gracefully ignoring non-textual content like embedded images and tables.
        *   A "successful" conversion is defined by:
            *   All textual content from chapters being processed.
            *   Audio files generated for all processed text blocks.
            *   Correct ordering of generated audio files reflecting the book's structure.
            *   No application crashes during processing.
            *   Graceful handling of TTS API rate limits or repeated errors: if a specific text block fails TTS conversion 'X' configured times, the application should complete processing for any other currently in-progress blocks for the current book, then pause or stop processing for that book, and provide a clear notification to the user about the issue.
    *   **PG2: Maintainable and Extensible Architecture:** The application will be developed in C# 9.0 with a modular design. This architecture should facilitate straightforward maintenance and simplify the future integration of planned enhancements.
    *   **PG3: Efficient Processing Performance:** The application should aim for an overall audio generation speed of approximately 5 times real-time (e.g., a 1-hour audio segment generated in 12 minutes) when utilizing 4 concurrent TTS processing threads, assuming individual TTS thread performance is around 1.5x real-time. This is a target to strive for, balancing speed with system stability and API limits.
    *   **PG4: Resilient Operation:** The application must be designed to resume processing from the last known good state after unexpected disruptions, such as application closure, PC crash, or temporary loss of network connectivity (for TTS requests).

---

**3. Target User and User Persona**

*   **3.1. Target User Profile:**
    Project Scribe is designed for individuals who are:
    *   **Technically Proficient:** Comfortable with computer technology, managing files and folders, and editing text-based configuration files.
    *   **Personal Use Focused:** Using the application for their own collection of ePubs to create audiobooks for personal consumption.
    *   **Quality Conscious:** Value high-quality audio output and are willing to configure a specific TTS service (Gemini) to achieve it.
    *   **Automation Seekers:** Prefer tools that can automate repetitive tasks.
    *   **Readers of Niche/Serialized Content:** May frequently read books or web serials unlikely to have official audiobook releases.

*   **3.2. User Persona: "Alex, the Automated Audiophile"**
    *   **Background:** Alex is a 35-year-old software developer who is an avid reader, especially of science fiction and fantasy series, including web novels. They have a large digital library of ePubs.
    *   **Goals related to Project Scribe:**
        *   Easily convert their extensive ePub collection into audiobooks.
        *   Achieve high-quality, consistent audio using Gemini TTS.
        *   Have a "set it and forget it" system.
        *   Easily transfer audio files to their smartphone.
    *   **Frustrations (Current State):** Lack of official audio for niche content, manual conversion is time-consuming and error-prone.
    *   **Technical Savviness:** Comfortable with Windows, file management, config editing, basic API concepts.
    *   **Quote:** *"I need a reliable tool that I can set up once to use my preferred TTS, point it at my library, and just have it work."*

---

**4. Functional Requirements**

*   **4.1. Configuration Management**
    *   **FR4.1.1:** The application MUST load its operational parameters from an external configuration file in JSON format (`config.json`). (See Section 6.5 for parameter list).
    *   **FR4.1.2:** The application MUST provide sensible default values for configuration parameters where appropriate if not specified by the user (or clearly indicate missing required parameters during startup).

*   **4.2. Directory Monitoring and Input File Ingestion**
    *   **FR4.2.1:** The application MUST continuously monitor the configured `InputDirectoryName` (and its subdirectories, relative to `BaseLibraryDirectory`) for new files matching the configured `SupportedInputFileExtensions` (initially `.epub`) using OS-level file system events.
    *   **FR4.2.2:** Upon detecting a new supported input file (e.g., `input\author_name\series_name\book_name.epub`), the application MUST initiate processing for that book.
    *   **FR4.2.3:** The application MUST maintain the `author_name\series_name\book_name` subdirectory structure when creating processed files in the `PrepareDirectoryName`, `WavDirectoryName`, and `OutputDirectoryName`. The original file extension will be stripped for directory naming if necessary.

*   **4.3. Book Content Parsing and Chapter Extraction**
    *   **FR4.3.1:** For each detected supported input file, the application MUST parse its content to extract individual chapters. Initially, parsing logic will be implemented for the ePub format using a suitable library such as `EpubReader`.
    *   **FR4.3.2:** The application MUST identify chapter breaks and process text content chapter by chapter, according to the structure of the input format.
    *   **FR4.3.3:** The application MUST ignore non-textual content within the input file (e.g., images, complex tables) and focus solely on extracting readable text.

*   **4.4. Text Processing and Splitting**
    *   **FR4.4.1:** The application MUST sanitize extracted text from each chapter by removing unreadable/problematic Unicode symbols.
    *   **FR4.4.2:** After sanitization, the application MUST split each chapter's text into smaller blocks.
    *   **FR4.4.3:** Text splitting SHOULD primarily occur at paragraph boundaries. If paragraphs are excessively long (exceeding `TextBlockTargetCharCount`), further splitting at sentence boundaries within that paragraph MAY be employed.
    *   **FR4.4.4:** The `TextBlockTargetCharCount` configuration parameter guides the splitting logic.
    *   **FR4.4.5:** The splitting algorithm MUST normalize block sizes to avoid very small trailing blocks, aiming for roughly equal sizes while respecting boundaries and the target size as a soft limit.
    *   **FR4.4.6:** Each processed text block MUST be saved as a separate `.txt` file (e.g., `0001.txt`) in the corresponding subdirectory under `PrepareDirectoryName`. Numbering must reflect original order.

*   **4.5. TTS Interaction (Gemini API)**
    *   **FR4.5.1:** The application MUST send each processed text block to the configured `GeminiTtsApiEndpoint`.
    *   **FR4.5.2:** The API request MUST include text content, the globally configured `GeminiTtsVoiceName`, `GeminiTtsModel`, and authorization token (from `.env` file).
    *   **FR4.5.3:** The application MUST manage concurrent TTS requests up to `MaxConcurrentTtsThreads`.
    *   **FR4.5.4:** The application MUST convert received base64-encoded PCM audio data to WAV format in memory by: (a) decoding base64 to raw PCM bytes, (b) extracting sample rate from `X-Sample-Rate` response header, (c) generating 44-byte WAV header with proper little-endian encoding, and (d) combining WAV header + PCM data.
    *   **FR4.5.5:** Converted WAV data MUST be saved as a `.wav` file in the corresponding subdirectory under `WavDirectoryName`.
    *   **FR4.5.6:** Generated WAV files MUST be verified (reasonable size, recognizable format, decent audio length).
    *   **FR4.5.7:** Failed TTS requests/verifications MUST be retried up to `MaxTtsRetries` times.
    *   **FR4.5.8:** Retries MUST use a backoff timer (`TtsRetryInitialDelayMs`, `TtsRetryBackoffMultiplier`).
    *   **FR4.5.9:** If a block fails all retries, log failure, cease attempts for this block, complete other in-progress blocks for the book, then pause/stop processing for that book and notify the user.

*   **4.6. Audio Post-Processing (Final Format Conversion)**
    *   **FR4.6.1:** Successfully generated WAV files MUST be concatenated in order. Concatenation and conversion SHOULD be done in batches, each aiming for a final audio file segment up to `MaxAudioBatchDurationMinutes`.
    *   **FR4.6.2:** Each batch of concatenated WAV audio MUST be converted into a separate audio file in the configured `FinalAudioFormat` (e.g., Opus or MP3) using an external tool like `ffmpeg`.
    *   **FR4.6.3:** The `FinalAudioBitrate` MUST be configurable.
    *   **FR4.6.4:** Final audio file(s) MUST be saved in the corresponding subdirectory under `OutputDirectoryName`, named appropriately (e.g., `book_name_part001.opus`).

*   **4.7. State Management and Resumability**
    *   **FR4.7.1:** Application state (processing progress per book/block) WILL be managed using an SQLite database.
    *   **FR4.7.2:** On restart, the application MUST query SQLite to identify incomplete jobs and resume processing from the last successfully completed step.
    *   **FR4.7.3:** Processed files should not be reprocessed if confirmed valid against state DB and file system.

*   **4.8. Logging and Notifications**
    *   **FR4.8.1:** Detailed operational logs WILL be written to a `/logs` subdirectory.
    *   **FR4.8.2:** Logs MUST be separated into files by log level (Trace, Debug, Info, Warn, Error).
    *   **FR4.8.3:** Standard log levels supported. `DefaultLogLevel` is configurable.
    *   **FR4.8.4:** Critical errors MUST result in clear user notification (console, ERROR log).

*   **4.9. Modularity for Future Enhancements**
    *   **FR4.9.1:** Architecture MUST be modular (input parsing, text processing, output encoding) to allow for new input parsers, text processing steps, or TTS services.

---

**5. Non-Functional Requirements (NFRs)**

*   **5.1. Performance**
    *   **NFR5.1.1:** Aim for ~5x real-time audio generation with 4 TTS threads (PG3).
    *   **NFR5.1.2:** Efficient directory monitoring via OS events.
    *   **NFR5.1.3:** Optimized SQLite operations.

*   **5.2. Reliability & Resilience**
    *   **NFR5.2.1:** Resumable processing after disruptions (FR4.7, PG4).
    *   **NFR5.2.2:** Robust error handling for TTS (FR4.5).
    *   **NFR5.2.3:** Graceful handling of common file system issues.

*   **5.3. Maintainability**
    *   **NFR5.3.1:** Adherence to SOLID principles.
    *   **NFR5.3.2:** Adherence to KISS and DRY principles.
    *   **NFR5.3.3:** Documentation Standard: XML docs for public classes/methods in `ProjectScribe.Logic`; inline comments only for complex, non-trivial logic.
    *   **NFR5.3.4:** Clear documentation for configuration parameters.
    *   **NFR5.3.5:** Design clarity suitable for junior developer comprehension and LLM-assisted development.

*   **5.4. Testability**
    *   **NFR5.4.1:** Core logic in `ProjectScribe.Logic` MUST be unit-testable.
    *   **NFR5.4.2:** Dedicated `ProjectScribe.Tests` project.
    *   **NFR5.4.3:** Tests to verify functionality after each significant processing step.

*   **5.5. Usability (for target technical user)**
    *   **NFR5.5.1:** Straightforward configuration via `config.json`.
    *   **NFR5.5.2:** Clear and informative logging.
    *   **NFR5.5.3:** "Set it and forget it" primary interaction mode.

*   **5.6. Extensibility**
    *   **NFR5.6.1:** Modular architecture for future enhancements (FR4.9). Apply YAGNI for current scope.

---

**6. Development and Technical Considerations**

*   **6.1. Solution Structure (C# 9.0):**
    *   `ProjectScribe.Console`: Main executable.
    *   `ProjectScribe.Logic`: Core business logic library.
    *   `ProjectScribe.Tests`: Unit and integration tests.

*   **6.2. Key Technologies & Libraries:**
    *   **DI:** `SimpleInject`.
    *   **EPUB Parsing:** `EpubReader`.
    *   **Database:** SQLite via `Microsoft.Data.Sqlite`.
    *   **Audio Conversion:** `ffmpeg` (external). App checks `3rdparty` subfolder, then `FfmpegPath` config.
    *   **Configuration:** JSON (`config.json`).
    *   **Logging:** `Microsoft.Extensions.Logging`. The recommended approach is to write your application and library code against the ILogger interface from Microsoft.Extensions.Logging. At startup, you configure MEL to use Serilog as the provider. This way, your code is decoupled from the logging framework, but you still get all the advanced features of Serilog for output
    *   **API Key Management:** Gemini TTS API key loaded from `.env` file (not in source control).

*   **6.3. TTS API Interaction Details (Gemini):**
    *   API key from `.env` (e.g., `YOUR_API_KEY=sk-proj-xxxxxxxx`).
    *   Example cURL for testing:
        ```bash
        # Ensure YOUR_API_KEY is set as an environment variable or replaced directly
        # Note: API now returns base64-encoded PCM data with X-Sample-Rate header
        curl -X POST "https://gemini-openai-adapter.100169.xyz/tts?voiceName=Charon" \
        -H "Authorization: Bearer $YOUR_API_KEY" \
        -H "Content-Type: application/json" \
        -d '{"text": "The inn was dark and empty when the traveller arrived.", "model": "gemini-2.5-flash-preview-tts" }' \
        --output output_base64.txt
        # Application must decode base64, extract sample rate from headers, and generate WAV
        ```
    *   Application must correctly format JSON payload and headers.

*   **6.4. Development Principles:**
    *   Adherence to SOLID, KISS, DRY, YAGNI.
    *   Iterative development.
    *   LLM-Assisted Development: Design for LLM generation/refinement, clarity for junior dev level.

*   **6.5. Configuration Parameters Summary (`config.json`):**
    *   `BaseLibraryDirectory` (string, e.g., "d:\\lib")
    *   `InputDirectoryName` (string, e.g., "input")
    *   `PrepareDirectoryName` (string, e.g., "prepare")
    *   `WavDirectoryName` (string, e.g., "wav")
    *   `OutputDirectoryName` (string, e.g., "output")
    *   `SupportedInputFileExtensions` (array of strings, e.g., `[".epub"]`)
    *   `GeminiTtsApiEndpoint` (string URL)
    *   `GeminiTtsVoiceName` (string, e.g., "Charon")
    *   `GeminiTtsModel` (string, e.g., "gemini-2.5-flash-preview-tts")
    *   `TextBlockTargetCharCount` (int, e.g., 1800)
    *   `MaxConcurrentTtsThreads` (int, e.g., 4)
    *   `MaxTtsRetries` (int, e.g., 3)
    *   `TtsRetryInitialDelayMs` (int, e.g., 5000)
    *   `TtsRetryBackoffMultiplier` (float, e.g., 2.0)
    *   `FinalAudioFormat` (string, e.g., "opus" or "mp3")
    *   `FinalAudioBitrate` (string, e.g., "96k" or "128k")
    *   `MaxAudioBatchDurationMinutes` (int, default: 60)
    *   `FfmpegPath` (string, optional, path to ffmpeg.exe if not bundled)
    *   `DefaultLogLevel` (string, e.g., "Information")

---

**7. Out of Scope (For Initial Version - V1.0)**

*   Graphical User Interface (GUI).
*   Advanced text preprocessing (e.g., automatic speaker personality extraction, emote tagging).
*   Automatic selection of male/female voices based on text content (global voice is used).
*   Support for input formats other than ePub (though architecture allows for future addition).
*   Direct integration with cloud storage providers for input/output.
*   User accounts or multi-user capabilities (designed for single-user personal use).

---

**8. Open Questions (For Future Consideration)**

*   **Advanced Voice Selection:** If multiple voices are desired per book, how would this be configured or determined (e.g., per-book config, in-text cues)?
*   **Error Handling Granularity:** More detailed user feedback mechanisms for specific types of ePub parsing errors.
*   **Resource Management:** More sophisticated monitoring of system resources (CPU/memory) beyond thread limits.
*   **Plugin Architecture for Text Processors:** Formalizing the modularity (FR4.9.1) into a more explicit plugin system for text manipulation steps.
