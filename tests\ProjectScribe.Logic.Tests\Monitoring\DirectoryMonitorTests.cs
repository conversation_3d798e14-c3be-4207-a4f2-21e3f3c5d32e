using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Monitoring;
using Xunit;

namespace ProjectScribe.Logic.Tests.Monitoring
{
    public class DirectoryMonitorTests : IDisposable
    {
        private readonly Mock<IConfigurationManager> _mockConfigManager;
        private readonly Mock<ILogger<DirectoryMonitor>> _mockLogger;
        private readonly string _testDirectory;
        private readonly AppSettings _testAppSettings;
        private readonly DirectoryMonitor _directoryMonitor;

        public DirectoryMonitorTests()
        {
            _mockConfigManager = new Mock<IConfigurationManager>();
            _mockLogger = new Mock<ILogger<DirectoryMonitor>>();
            
            // Create a unique test directory for each test run
            _testDirectory = Path.Combine(Path.GetTempPath(), "ProjectScribeTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            
            _testAppSettings = new AppSettings
            {
                BaseLibraryDirectory = _testDirectory,
                InputDirectoryName = "input",
                SupportedInputFileExtensions = new List<string> { ".epub", ".txt" }
            };
            
            _mockConfigManager.Setup(x => x.GetAppSettings()).Returns(_testAppSettings);
            _directoryMonitor = new DirectoryMonitor(_mockConfigManager.Object, _mockLogger.Object);
        }

        [Fact]
        public void Constructor_WithNullConfigurationManager_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new DirectoryMonitor(null!, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new DirectoryMonitor(_mockConfigManager.Object, null!));
        }

        [Fact]
        public void IsMonitoring_InitiallyReturnsFalse()
        {
            // Act & Assert
            Assert.False(_directoryMonitor.IsMonitoring);
        }

        [Fact]
        public void StartMonitoring_CreatesInputDirectoryIfNotExists()
        {
            // Arrange
            var inputPath = Path.Combine(_testDirectory, "input");
            Assert.False(Directory.Exists(inputPath));

            // Act
            _directoryMonitor.StartMonitoring();

            // Assert
            Assert.True(Directory.Exists(inputPath));
            Assert.True(_directoryMonitor.IsMonitoring);
        }

        [Fact]
        public void StartMonitoring_WithExistingDirectory_StartsSuccessfully()
        {
            // Arrange
            var inputPath = Path.Combine(_testDirectory, "input");
            Directory.CreateDirectory(inputPath);

            // Act
            _directoryMonitor.StartMonitoring();

            // Assert
            Assert.True(_directoryMonitor.IsMonitoring);
        }

        [Fact]
        public void StartMonitoring_WhenAlreadyMonitoring_LogsWarning()
        {
            // Arrange
            _directoryMonitor.StartMonitoring();
            Assert.True(_directoryMonitor.IsMonitoring);

            // Act
            _directoryMonitor.StartMonitoring();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("already active")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void StartMonitoring_WithNoSupportedExtensions_LogsErrorAndDoesNotStart()
        {
            // Arrange
            _testAppSettings.SupportedInputFileExtensions = new List<string>();

            // Act
            _directoryMonitor.StartMonitoring();

            // Assert
            Assert.False(_directoryMonitor.IsMonitoring);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("No supported input file extensions")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void StopMonitoring_WhenNotMonitoring_DoesNotThrow()
        {
            // Act & Assert
            _directoryMonitor.StopMonitoring(); // Should not throw
        }

        [Fact]
        public void StopMonitoring_WhenMonitoring_StopsSuccessfully()
        {
            // Arrange
            _directoryMonitor.StartMonitoring();
            Assert.True(_directoryMonitor.IsMonitoring);

            // Act
            _directoryMonitor.StopMonitoring();

            // Assert
            Assert.False(_directoryMonitor.IsMonitoring);
        }

        [Fact]
        public async Task NewFileDetected_WithSupportedExtension_RaisesEvent()
        {
            // Arrange
            var inputPath = Path.Combine(_testDirectory, "input");
            Directory.CreateDirectory(inputPath);
            
            var eventRaised = false;
            string? detectedFilePath = null;
            
            _directoryMonitor.NewFileDetected += (sender, filePath) =>
            {
                eventRaised = true;
                detectedFilePath = filePath;
            };

            _directoryMonitor.StartMonitoring();

            // Act
            var testFilePath = Path.Combine(inputPath, "test.epub");
            await File.WriteAllTextAsync(testFilePath, "test content");
            
            // Wait a bit for the file system event to be processed
            await Task.Delay(500);

            // Assert
            Assert.True(eventRaised);
            Assert.Equal(testFilePath, detectedFilePath);
        }

        [Fact]
        public async Task NewFileDetected_WithUnsupportedExtension_DoesNotRaiseEvent()
        {
            // Arrange
            var inputPath = Path.Combine(_testDirectory, "input");
            Directory.CreateDirectory(inputPath);
            
            var eventRaised = false;
            
            _directoryMonitor.NewFileDetected += (sender, filePath) =>
            {
                eventRaised = true;
            };

            _directoryMonitor.StartMonitoring();

            // Act
            var testFilePath = Path.Combine(inputPath, "test.pdf");
            await File.WriteAllTextAsync(testFilePath, "test content");
            
            // Wait a bit for the file system event to be processed
            await Task.Delay(500);

            // Assert
            Assert.False(eventRaised);
        }

        [Fact]
        public async Task NewFileDetected_InSubdirectory_RaisesEvent()
        {
            // Arrange
            var inputPath = Path.Combine(_testDirectory, "input");
            var subDirPath = Path.Combine(inputPath, "author", "series");
            Directory.CreateDirectory(subDirPath);
            
            var eventRaised = false;
            string? detectedFilePath = null;
            
            _directoryMonitor.NewFileDetected += (sender, filePath) =>
            {
                eventRaised = true;
                detectedFilePath = filePath;
            };

            _directoryMonitor.StartMonitoring();

            // Act
            var testFilePath = Path.Combine(subDirPath, "book.epub");
            await File.WriteAllTextAsync(testFilePath, "test content");
            
            // Wait a bit for the file system event to be processed
            await Task.Delay(500);

            // Assert
            Assert.True(eventRaised);
            Assert.Equal(testFilePath, detectedFilePath);
        }

        [Fact]
        public void Dispose_AfterStartMonitoring_StopsMonitoringAndDisposesResources()
        {
            // Arrange
            _directoryMonitor.StartMonitoring();
            Assert.True(_directoryMonitor.IsMonitoring);

            // Act
            _directoryMonitor.Dispose();

            // Assert
            Assert.False(_directoryMonitor.IsMonitoring);
        }

        [Fact]
        public void Dispose_WhenNotMonitoring_DoesNotThrow()
        {
            // Act & Assert
            _directoryMonitor.Dispose(); // Should not throw
        }

        [Fact]
        public void StartMonitoring_AfterDispose_ThrowsObjectDisposedException()
        {
            // Arrange
            _directoryMonitor.Dispose();

            // Act & Assert
            Assert.Throws<ObjectDisposedException>(() => _directoryMonitor.StartMonitoring());
        }

        public void Dispose()
        {
            _directoryMonitor?.Dispose();
            
            // Clean up test directory
            if (Directory.Exists(_testDirectory))
            {
                try
                {
                    Directory.Delete(_testDirectory, true);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
    }
}
