using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Common;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Processing;
using ProjectScribe.Logic.State;
using SimpleInjector;
using System.Collections.Concurrent;

namespace ProjectScribe.Console.Services
{
    /// <summary>
    /// Handles application startup tasks including database initialization, 
    /// incomplete book processing, and existing file detection.
    /// </summary>
    public class StartupService
    {
        private readonly Container _container;
        private readonly ILogger<StartupService> _logger;
        private readonly IStateManager _stateManager;
        private readonly IConfigurationManager _configurationManager;
        
        // Static dictionary to track books currently being processed to prevent concurrent processing of the same book
        private static readonly ConcurrentDictionary<string, bool> _booksBeingProcessed = new();

        public StartupService(
            Container container,
            ILogger<StartupService> logger,
            IStateManager stateManager,
            IConfigurationManager configurationManager)
        {
            _container = container;
            _logger = logger;
            _stateManager = stateManager;
            _configurationManager = configurationManager;
        }

        /// <summary>
        /// Initializes the application state and processes any pending work.
        /// </summary>
        public async Task InitializeAsync()
        {
            _logger.LogInformation("Initializing application state...");
            
            try
            {
                // Initialize database
                await _stateManager.InitializeDatabaseAsync();
                _logger.LogInformation("Database initialized successfully");

                // Process any incomplete books from previous runs
                await ProcessIncompleteBooks();

                // Process any existing files in input directory that haven't been tracked yet
                await ProcessExistingFiles();
                
                _logger.LogInformation("Application state initialization completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during application state initialization");
                throw; // Re-throw to prevent application from continuing with invalid state
            }
        }

        /// <summary>
        /// Safely processes a book with synchronization to prevent concurrent processing of the same book.
        /// </summary>
        public static async Task SafeProcessBookAsync(Container container, ILogger logger, string filePath, string context)
        {
            var bookId = FileSystemUtils.GenerateBookId(filePath);

            // Check if this book is already being processed
            if (!_booksBeingProcessed.TryAdd(bookId, true))
            {
                logger.LogInformation("Book {BookId} is already being processed, skipping {Context} processing for {FilePath}",
                    bookId, context, filePath);
                return;
            }

            try
            {
                using (SimpleInjector.Lifestyles.ThreadScopedLifestyle.BeginScope(container))
                {
                    var bookProcessor = container.GetInstance<IBookProcessor>();
                    logger.LogInformation("{Context}: Processing book {BookId} from {FilePath}", context, bookId, filePath);
                    await bookProcessor.ProcessBookAsync(filePath);
                    logger.LogInformation("{Context}: Successfully processed book {BookId}", context, bookId);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "{Context}: Error processing book {BookId}: {Error}", context, bookId, ex.Message);
            }
            finally
            {
                // Always remove the book from the processing dictionary when done
                _booksBeingProcessed.TryRemove(bookId, out _);
            }
        }

        /// <summary>
        /// Processes any incomplete books from previous application runs.
        /// This enables resumability by automatically continuing processing where it left off.
        /// </summary>
        private async Task ProcessIncompleteBooks()
        {
            try
            {
                var incompleteBooks = await _stateManager.GetIncompleteBooksAsync();
                var incompleteBooksList = incompleteBooks.ToList();

                if (!incompleteBooksList.Any())
                {
                    _logger.LogInformation("No incomplete books found from previous runs");
                    return;
                }

                _logger.LogInformation("Found {IncompleteCount} incomplete books from previous runs, starting resumption processing",
                    incompleteBooksList.Count);

                // Process each incomplete book sequentially to avoid file conflicts
                foreach (var bookState in incompleteBooksList)
                {
                    await SafeProcessBookAsync(_container, _logger, bookState.FilePath, "Resume");
                }

                _logger.LogInformation("Completed resumption processing for all incomplete books");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during incomplete books processing: {Error}", ex.Message);
                // Don't throw - allow application to continue with normal operation
            }
        }

        /// <summary>
        /// Scans the input directory for existing files that haven't been processed yet.
        /// This ensures that files already present in the input directory are detected and processed on startup.
        /// </summary>
        private async Task ProcessExistingFiles()
        {
            try
            {
                var appSettings = _configurationManager.GetAppSettings();
                var inputDirectory = Path.Combine(appSettings.BaseLibraryDirectory, appSettings.InputDirectoryName);

                // Check if input directory exists
                if (!Directory.Exists(inputDirectory))
                {
                    _logger.LogInformation("Input directory does not exist: {InputDirectory}", inputDirectory);
                    return;
                }

                // Get all supported files in the input directory (including subdirectories)
                var supportedExtensions = appSettings.SupportedInputFileExtensions ?? new List<string>();
                if (!supportedExtensions.Any())
                {
                    _logger.LogWarning("No supported file extensions configured");
                    return;
                }

                var existingFiles = new List<string>();
                foreach (var extension in supportedExtensions)
                {
                    var pattern = $"*{extension}";
                    var files = Directory.GetFiles(inputDirectory, pattern, SearchOption.AllDirectories);
                    existingFiles.AddRange(files);
                }

                if (!existingFiles.Any())
                {
                    _logger.LogInformation("No existing files found in input directory: {InputDirectory}", inputDirectory);
                    return;
                }

                _logger.LogInformation("Found {FileCount} existing files in input directory", existingFiles.Count);

                // Check each file to see if it's already being tracked
                var newFiles = new List<string>();
                foreach (var filePath in existingFiles)
                {
                    try
                    {
                        var bookId = FileSystemUtils.GenerateBookId(filePath);
                        var bookExists = await _stateManager.BookExistsAsync(bookId);

                        if (!bookExists)
                        {
                            newFiles.Add(filePath);
                            _logger.LogInformation("Found untracked file: {FilePath}", filePath);
                        }
                        else
                        {
                            _logger.LogDebug("File already tracked: {FilePath}", filePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error checking file status: {FilePath}", filePath);
                    }
                }

                if (!newFiles.Any())
                {
                    _logger.LogInformation("All existing files are already tracked in the database");
                    return;
                }

                _logger.LogInformation("Processing {NewFileCount} untracked files", newFiles.Count);

                // Process each new file sequentially to avoid file conflicts
                foreach (var filePath in newFiles)
                {
                    await SafeProcessBookAsync(_container, _logger, filePath, "ExistingFile");
                }
                
                _logger.LogInformation("Completed processing existing files");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing existing files: {Error}", ex.Message);
                // Don't throw - existing file processing failures shouldn't prevent the application from starting
            }
        }
    }
}
