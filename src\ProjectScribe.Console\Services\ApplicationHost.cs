using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Audio;
using ProjectScribe.Logic.Configuration;
using ProjectScribe.Logic.Logging;
using ProjectScribe.Logic.Monitoring;
using ProjectScribe.Logic.Parsing;
using ProjectScribe.Logic.Processing;
using ProjectScribe.Logic.State;
using ProjectScribe.Logic.Text;
using ProjectScribe.Logic.TTS;
using SimpleInjector;

namespace ProjectScribe.Console.Services
{
    /// <summary>
    /// Manages the application lifecycle, dependency injection setup, and coordination between services.
    /// </summary>
    public class ApplicationHost : IDisposable
    {
        private Container? _container;
        private ILogger<ApplicationHost>? _logger;
        private ApplicationRunner? _applicationRunner;
        private SingleInstanceService? _singleInstanceService;
        private bool _disposed;

        /// <summary>
        /// Runs the application from start to finish.
        /// </summary>
        public async Task RunAsync()
        {
            try
            {
                // Setup dependency injection
                SetupDependencyInjection();

                // Initialize logging
                _logger = _container!.GetInstance<ILogger<ApplicationHost>>();

                // Check for single instance before proceeding
                _singleInstanceService = _container.GetInstance<SingleInstanceService>();
                if (!_singleInstanceService.TryAcquireInstance())
                {
                    _logger.LogError("Another instance of ProjectScribe is already running. Only one instance is allowed at a time.");
                    System.Console.WriteLine("ERROR: Another instance of ProjectScribe is already running.");
                    System.Console.WriteLine("Please close the other instance before starting a new one.");
                    Environment.Exit(1);
                    return;
                }

                _logger.LogInformation("ProjectScribe Console Application started successfully");
                _logger.LogInformation("Configuration loaded and DI container verified");

                // Set console window title and minimize to taskbar
                var consoleWindowService = _container.GetInstance<ConsoleWindowService>();
                if (consoleWindowService.IsSupported)
                {
                    consoleWindowService.SetWindowTitle("ProjectScribe - Audio Book Processor");

                    // Minimize to taskbar on startup
                    if (consoleWindowService.MinimizeToTaskbar())
                    {
                        _logger.LogInformation("Console window minimized to taskbar on startup");
                    }
                }
                else
                {
                    _logger.LogInformation("Console window management not supported on this platform");
                }

                // Log configuration details
                LogConfigurationDetails();

                // Initialize application state and startup processing
                using (SimpleInjector.Lifestyles.ThreadScopedLifestyle.BeginScope(_container))
                {
                    var startupService = _container.GetInstance<StartupService>();
                    await startupService.InitializeAsync();
                }

                // Run the main application
                _applicationRunner = _container.GetInstance<ApplicationRunner>();
                await _applicationRunner.RunAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Fatal error during application execution");
                throw;
            }
            finally
            {
                await ShutdownAsync();
            }
        }

        /// <summary>
        /// Sets up the dependency injection container with all required services.
        /// </summary>
        private void SetupDependencyInjection()
        {
            _container = new Container();

            // Configure default scoped lifestyle for SimpleInjector
            _container.Options.DefaultScopedLifestyle = new SimpleInjector.Lifestyles.ThreadScopedLifestyle();

            RegisterCoreServices();
            RegisterBusinessServices();
            RegisterLoggingServices();
            RegisterApplicationServices();

            // Verify the container's configuration
            _container.Verify();
        }

        /// <summary>
        /// Registers core infrastructure services.
        /// </summary>
        private void RegisterCoreServices()
        {
            // Configuration services
            _container!.Register<IConfigurationManager, ConfigurationManager>(Lifestyle.Singleton);
            _container.Register(() => _container.GetInstance<IConfigurationManager>().GetAppSettings(), Lifestyle.Singleton);

            // Database services
            _container.Register(() =>
            {
                var appSettings = _container.GetInstance<AppSettings>();
                var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                optionsBuilder.UseSqlite($"Data Source={appSettings.DatabasePath}");
                return new AppDbContext(optionsBuilder.Options);
            }, Lifestyle.Scoped);
            
            _container.Register<IStateManager, StateManager>(Lifestyle.Scoped);

            // HTTP Client for external services
            _container.RegisterSingleton<HttpClient>(() => new HttpClient());
        }

        /// <summary>
        /// Registers business logic services.
        /// </summary>
        private void RegisterBusinessServices()
        {
            // File monitoring
            _container!.Register<IDirectoryMonitor, DirectoryMonitor>(Lifestyle.Singleton);

            // Processing pipeline services
            _container.Register<IInputParser, EpubInputParser>(Lifestyle.Singleton);
            _container.Register<ITextProcessor, TextProcessor>(Lifestyle.Singleton);
            _container.Register<IStyleProcessor, StyleProcessor>(Lifestyle.Singleton);
            _container.Register<ITtsClient, GeminiTtsClient>(Lifestyle.Scoped);
            
            // Audio processing services
            _container.Register<IFfmpegClient, FfmpegClient>(Lifestyle.Singleton);
            _container.Register<IAudioPostProcessor, AudioPostProcessor>(Lifestyle.Singleton);

            // Main orchestrator
            _container.Register<IBookProcessor, BookProcessor>(Lifestyle.Scoped);
        }

        /// <summary>
        /// Registers logging services using Serilog.
        /// </summary>
        private void RegisterLoggingServices()
        {
            _container!.RegisterSingleton<Serilog.Core.Logger>(() => 
                SerilogSetup.CreateLogger(_container.GetInstance<IConfigurationManager>()));
            
            _container.RegisterSingleton<ILoggerFactory>(() => 
                SerilogSetup.CreateLoggerFactory(_container.GetInstance<Serilog.Core.Logger>()));
            
            // Register generic ILogger<T> - SimpleInjector will handle this automatically
            _container.Register(typeof(ILogger<>), typeof(Logger<>), Lifestyle.Singleton);
            
            // Fallback for non-generic ILogger
            _container.Register<ILogger>(() => 
                _container.GetInstance<ILoggerFactory>().CreateLogger("Default"), Lifestyle.Singleton);
        }

        /// <summary>
        /// Registers application-specific services.
        /// </summary>
        private void RegisterApplicationServices()
        {
            _container!.Register<StartupService>(Lifestyle.Scoped);
            _container.Register<ApplicationRunner>(Lifestyle.Singleton);
            _container.Register<SingleInstanceService>(Lifestyle.Singleton);
            _container.Register<ConsoleWindowService>(Lifestyle.Singleton);
        }

        /// <summary>
        /// Logs important configuration details for debugging and monitoring.
        /// </summary>
        private void LogConfigurationDetails()
        {
            if (_logger == null) return;

            var appSettings = _container!.GetInstance<AppSettings>();
            _logger.LogInformation("Base Library Directory: {BaseLibraryDirectory}", appSettings.BaseLibraryDirectory);
            _logger.LogInformation("Input Directory: {InputDirectory}",
                Path.Combine(appSettings.BaseLibraryDirectory, appSettings.InputDirectoryName));
            _logger.LogInformation("Output Directory: {OutputDirectory}",
                Path.Combine(appSettings.BaseLibraryDirectory, appSettings.OutputDirectoryName));
            _logger.LogInformation("Supported File Extensions: {Extensions}",
                string.Join(", ", appSettings.SupportedInputFileExtensions ?? new List<string>()));
        }

        /// <summary>
        /// Performs graceful shutdown of the application.
        /// </summary>
        private async Task ShutdownAsync()
        {
            try
            {
                // Stop the application runner if it's running
                if (_applicationRunner != null)
                {
                    await _applicationRunner.StopAsync();
                }

                // Release single instance lock
                _singleInstanceService?.ReleaseInstance();

                // Flush and dispose logging
                if (_container != null)
                {
                    var serilogCoreLogger = _container.GetInstance<Serilog.Core.Logger>();
                    serilogCoreLogger.Dispose();
                }

                if (_logger != null)
                {
                    _logger.LogInformation("Application shutdown complete");
                }
                System.Console.WriteLine("Application shutdown complete.");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"Error during shutdown: {ex.Message}");
            }
        }

        /// <summary>
        /// Disposes the application host and releases all resources.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            try
            {
                // Release single instance lock
                _singleInstanceService?.Dispose();

                // Dispose container
                _container?.Dispose();
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"Error during disposal: {ex.Message}");
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Generic logger implementation for dependency injection.
    /// </summary>
    internal class Logger<T> : ILogger<T>
    {
        private readonly ILogger _logger;

        public Logger(ILoggerFactory loggerFactory)
        {
            _logger = loggerFactory.CreateLogger<T>();
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull => _logger.BeginScope(state);
        public bool IsEnabled(LogLevel logLevel) => _logger.IsEnabled(logLevel);
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            _logger.Log(logLevel, eventId, state, exception, formatter);
        }
    }
}
