using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectScribe.Logic.Audio
{
    /// <summary>
    /// Implementation of IAudioPostProcessor for processing batches of WAV files.
    /// <PERSON>les concatenation and conversion to final audio formats using FFmpeg.
    /// </summary>
    public class AudioPostProcessor : IAudioPostProcessor
    {
        private readonly IConfigurationManager _configurationManager;
        private readonly IFfmpegClient _ffmpegClient;
        private readonly ILogger<AudioPostProcessor> _logger;

        /// <summary>
        /// Initializes a new instance of the AudioPostProcessor class.
        /// </summary>
        /// <param name="configurationManager">Configuration manager for accessing audio processing settings.</param>
        /// <param name="ffmpegClient">FFmpeg client for audio operations.</param>
        /// <param name="logger">Logger for recording audio processing operations.</param>
        /// <exception cref="ArgumentNullException">Thrown when any parameter is null.</exception>
        public AudioPostProcessor(
            IConfigurationManager configurationManager,
            IFfmpegClient ffmpegClient,
            ILogger<AudioPostProcessor> logger)
        {
            _configurationManager = configurationManager ?? throw new ArgumentNullException(nameof(configurationManager));
            _ffmpegClient = ffmpegClient ?? throw new ArgumentNullException(nameof(ffmpegClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Processes a batch of WAV files by concatenating them and converting to the final audio format
        /// </summary>
        /// <param name="wavFilePaths">Collection of WAV file paths to process in order</param>
        /// <param name="bookOutputDirectory">The output directory for the book</param>
        /// <param name="bookName">Name of the book for file naming</param>
        /// <param name="batchNumber">Batch number for file naming (e.g., part001, part002)</param>
        /// <returns>Path to the final audio file if successful, null if failed</returns>
        public async Task<string?> ProcessBatchAsync(
            IEnumerable<string> wavFilePaths, 
            string bookOutputDirectory, 
            string bookName, 
            int batchNumber)
        {
            if (wavFilePaths == null || !wavFilePaths.Any())
            {
                _logger.LogError("No WAV files provided for batch processing");
                return null;
            }

            if (string.IsNullOrWhiteSpace(bookOutputDirectory))
            {
                _logger.LogError("Book output directory is required");
                return null;
            }

            if (string.IsNullOrWhiteSpace(bookName))
            {
                _logger.LogError("Book name is required");
                return null;
            }

            try
            {
                var appSettings = _configurationManager.GetAppSettings();
                var inputFiles = wavFilePaths.ToList();

                _logger.LogInformation("Processing audio batch {BatchNumber} for book '{BookName}' with {FileCount} WAV files",
                    batchNumber, bookName, inputFiles.Count);

                // Validate all input files exist
                foreach (var filePath in inputFiles)
                {
                    if (!File.Exists(filePath))
                    {
                        _logger.LogError("WAV file not found: {FilePath}", filePath);
                        return null;
                    }
                }

                // Generate output file name and path
                var outputFileName = GenerateOutputFileName(bookName, batchNumber, appSettings.FinalAudioFormat);
                var outputFilePath = Path.Combine(bookOutputDirectory, outputFileName);

                _logger.LogDebug("Output file will be: {OutputPath}", outputFilePath);

                // Ensure output directory exists
                if (!Directory.Exists(bookOutputDirectory))
                {
                    Directory.CreateDirectory(bookOutputDirectory);
                    _logger.LogDebug("Created output directory: {Directory}", bookOutputDirectory);
                }

                // Create metadata for the audiobook
                var metadata = CreateAudiobookMetadata(bookName, batchNumber);

                // Process the batch using ffmpeg
                var success = await _ffmpegClient.ConcatenateAndConvertAsync(
                    inputFiles,
                    outputFilePath,
                    appSettings.FinalAudioFormat,
                    appSettings.FinalAudioBitrate,
                    metadata);

                if (success)
                {
                    // Verify the output file was created and has content
                    if (File.Exists(outputFilePath))
                    {
                        var fileInfo = new FileInfo(outputFilePath);
                        if (fileInfo.Length > 0)
                        {
                            _logger.LogInformation("Successfully processed batch {BatchNumber} for book '{BookName}'. Output: {OutputPath} ({Size} bytes)",
                                batchNumber, bookName, outputFilePath, fileInfo.Length);
                            return outputFilePath;
                        }
                        else
                        {
                            _logger.LogError("Output file was created but is empty: {OutputPath}", outputFilePath);
                            return null;
                        }
                    }
                    else
                    {
                        _logger.LogError("FFmpeg reported success but output file was not created: {OutputPath}", outputFilePath);
                        return null;
                    }
                }
                else
                {
                    _logger.LogError("Failed to process batch {BatchNumber} for book '{BookName}'", batchNumber, bookName);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing audio batch {BatchNumber} for book '{BookName}'", batchNumber, bookName);
                return null;
            }
        }

        /// <summary>
        /// Generates the output file name for a processed audio batch
        /// </summary>
        /// <param name="bookName">Name of the book</param>
        /// <param name="batchNumber">Batch number</param>
        /// <param name="format">Audio format extension</param>
        /// <returns>Generated file name</returns>
        private string GenerateOutputFileName(string bookName, int batchNumber, string format)
        {
            // Sanitize book name for file system
            var sanitizedBookName = SanitizeFileName(bookName);
            
            // Format batch number with leading zeros (e.g., part001, part002)
            var batchSuffix = $"part{batchNumber:D3}";
            
            // Determine file extension based on format
            var extension = GetFileExtension(format);
            
            return $"{sanitizedBookName}_{batchSuffix}.{extension}";
        }

        /// <summary>
        /// Creates metadata for audiobook files
        /// </summary>
        /// <param name="bookName">Name of the book</param>
        /// <param name="batchNumber">Batch/part number</param>
        /// <returns>AudioMetadata object with appropriate audiobook metadata</returns>
        private AudioMetadata CreateAudiobookMetadata(string bookName, int batchNumber)
        {
            var metadata = new AudioMetadata
            {
                Title = batchNumber == 1 ? bookName : $"{bookName} - Part {batchNumber:D3}",
                Album = bookName,
                Artist = "Unknown Author", // Could be enhanced to extract from book metadata
                AlbumArtist = "ProjectScribe TTS", // Could be enhanced with actual narrator info
                Track = batchNumber,
                Genre = "Audiobook",
                Comment = $"Generated by ProjectScribe - Part {batchNumber:D3}"
            };

            return metadata;
        }

        /// <summary>
        /// Sanitizes a string to be safe for use as a file name
        /// </summary>
        /// <param name="fileName">Original file name</param>
        /// <returns>Sanitized file name</returns>
        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "unknown";

            // Remove or replace invalid characters and spaces
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Also replace spaces with underscores for cleaner file names
            sanitized = sanitized.Replace(' ', '_');

            // Replace multiple consecutive underscores with single underscore
            while (sanitized.Contains("__"))
            {
                sanitized = sanitized.Replace("__", "_");
            }

            // Trim underscores from start and end
            sanitized = sanitized.Trim('_');

            // Ensure it's not empty after sanitization
            if (string.IsNullOrWhiteSpace(sanitized))
                sanitized = "unknown";

            // Limit length to reasonable file name length
            if (sanitized.Length > 100)
                sanitized = sanitized.Substring(0, 100).TrimEnd('_');

            return sanitized;
        }

        /// <summary>
        /// Gets the appropriate file extension for the given audio format
        /// </summary>
        /// <param name="format">Audio format</param>
        /// <returns>File extension without the dot</returns>
        private string GetFileExtension(string format)
        {
            return format.ToLowerInvariant() switch
            {
                "opus" => "opus",
                "mp3" => "mp3",
                "aac" => "aac",
                "wav" => "wav",
                "ogg" => "ogg",
                _ => format.ToLowerInvariant() // Default to the format name
            };
        }
    }
}
