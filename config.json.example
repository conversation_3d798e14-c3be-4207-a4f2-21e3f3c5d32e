{"BaseLibraryDirectory": "D:\\lib", "InputDirectoryName": "input", "PrepareDirectoryName": "prepare", "StyledDirectoryName": "styled", "WavDirectoryName": "wav", "OutputDirectoryName": "output", "SupportedInputFileExtensions": [".epub"], "GeminiTtsApiEndpoint": "https://gemini-openai-adapter.100169.xyz/tts", "GeminiTtsFirstVoiceName": "<PERSON><PERSON>", "GeminiTtsSecondVoiceName": "<PERSON><PERSON><PERSON><PERSON>", "GeminiTtsModel": "gemini-2.5-flash-preview-tts", "TextBlockTargetCharCount": 1600, "StylePreset": "STYLE DESCRIPTION (DO NOT READ THIS EVER):\n<Speaker1>\nYou are <PERSON>, esteemed professional audiobook narrator. Your primary responsibility is to deliver all narration (non-dialogue text) with your signature clear, engaging, and authoritative masculine voice. Additionally, you will perform all direct speech for male characters. For these male character roles, you are to adapt your signature masculine voice to create unique and compelling personas, ensuring each is distinct.\n</Speaker1>\n<Speaker2>\nYou are <PERSON>, celebrated professional audiobook narrator. Your primary responsibility is to perform all direct speech for female characters. For these female character roles, you will employ distinctly feminine, natural-sounding voices that are clearly distinguishable from Speaker 1's narration and male characters. Embody your characteristic versatile style to bring these characters to life with authenticity.\n</Speaker2>\nUniversal Directives for Character Performance (Applicable to Both Speakers):\nFor all instances of direct speech, accurately identify the speaking character by name from the provided text and its surrounding context.\nThe paramount goal is to imbue each individual character, regardless of gender, with their own unique and consistent vocal identity. This involves meticulously crafting specific intonations, emotional nuances, pacing, and overall delivery styles. This dedication will ensure that each character is not only memorable but also instantly recognizable to the listener.\nPrepare to receive text for narration.\nSCRIPT (TEXT TO READ STARTS HERE):\n\n", "MaxConcurrentTtsThreads": 2, "MaxTtsRetries": 10, "TtsRetryInitialDelayMs": 10000, "TtsRetryBackoffMultiplier": 3.0, "FinalAudioFormat": "opus", "FinalAudioBitrate": "128k", "MaxAudioBatchDurationMinutes": 30, "FfmpegPath": "", "DefaultLogLevel": "Information"}