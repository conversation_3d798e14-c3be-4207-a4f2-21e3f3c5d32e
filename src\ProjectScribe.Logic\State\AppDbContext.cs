using Microsoft.EntityFrameworkCore;
using ProjectScribe.Logic.State.Entities;

namespace ProjectScribe.Logic.State
{
    /// <summary>
    /// Entity Framework Core database context for Project Scribe state management.
    /// Manages SQLite database operations for tracking book and text block processing states.
    /// </summary>
    public class AppDbContext : DbContext
    {
        /// <summary>
        /// Database set for book processing states.
        /// </summary>
        public DbSet<BookProcessState> BookProcessStates { get; set; }

        /// <summary>
        /// Database set for text block processing states.
        /// </summary>
        public DbSet<TextBlockState> TextBlockStates { get; set; }

        /// <summary>
        /// Initializes a new instance of the AppDbContext with the specified options.
        /// </summary>
        /// <param name="options">Database context options.</param>
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// Configures the entity models and relationships.
        /// </summary>
        /// <param name="modelBuilder">The model builder instance.</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure BookProcessState entity
            modelBuilder.Entity<BookProcessState>(entity =>
            {
                entity.HasKey(e => e.BookId);
                
                entity.Property(e => e.BookId)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.FilePath)
                    .IsRequired()
                    .HasMaxLength(1000);

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasConversion<string>(); // Store enum as string for readability

                entity.Property(e => e.LastProcessedStep)
                    .HasMaxLength(500);

                entity.Property(e => e.ErrorMessage)
                    .HasMaxLength(2000);

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // Index for efficient querying by status
                entity.HasIndex(e => e.Status)
                    .HasDatabaseName("IX_BookProcessState_Status");

                // Index for efficient querying by creation date
                entity.HasIndex(e => e.CreatedAt)
                    .HasDatabaseName("IX_BookProcessState_CreatedAt");
            });

            // Configure TextBlockState entity
            modelBuilder.Entity<TextBlockState>(entity =>
            {
                entity.HasKey(e => e.TextBlockId);

                entity.Property(e => e.TextBlockId)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.BookId)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.BlockSequenceNumber)
                    .IsRequired();

                entity.Property(e => e.TextFilePath)
                    .IsRequired()
                    .HasMaxLength(1000);

                entity.Property(e => e.WavFilePath)
                    .HasMaxLength(1000);

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasConversion<string>(); // Store enum as string for readability

                entity.Property(e => e.RetryCount)
                    .IsRequired()
                    .HasDefaultValue(0);

                entity.Property(e => e.ErrorMessage)
                    .HasMaxLength(2000);

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // Configure foreign key relationship
                entity.HasOne(e => e.Book)
                    .WithMany(b => b.TextBlocks)
                    .HasForeignKey(e => e.BookId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Index for efficient querying by book and sequence
                entity.HasIndex(e => new { e.BookId, e.BlockSequenceNumber })
                    .HasDatabaseName("IX_TextBlockState_BookId_Sequence")
                    .IsUnique();

                // Index for efficient querying by status
                entity.HasIndex(e => e.Status)
                    .HasDatabaseName("IX_TextBlockState_Status");

                // Index for efficient querying by book and status
                entity.HasIndex(e => new { e.BookId, e.Status })
                    .HasDatabaseName("IX_TextBlockState_BookId_Status");
            });
        }

        /// <summary>
        /// Override SaveChanges to automatically update UpdatedAt timestamps.
        /// </summary>
        /// <returns>The number of state entries written to the database.</returns>
        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        /// <summary>
        /// Override SaveChangesAsync to automatically update UpdatedAt timestamps.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>The number of state entries written to the database.</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp for modified entities.
        /// </summary>
        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is BookProcessState bookState)
                {
                    bookState.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is TextBlockState textBlockState)
                {
                    textBlockState.UpdatedAt = DateTime.UtcNow;
                }
            }
        }
    }
}
