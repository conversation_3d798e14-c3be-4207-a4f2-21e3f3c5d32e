# Technical Design and Architecture: Project Scribe
**Version:** 1.0
**Date:** June 2, 2025
**Author:** Rooroo Documenter
## Table of Contents
1.  Core System Components/Modules
2.  Architectural Patterns
3.  Conceptual Data Flows
4.  Technology Stack Implications (High-Level)
5.  Non-Functional Requirements (NFRs) Support
6.  Diagrammatic Representation Suggestions
---
## 1. Core System Components/Modules
Based on the requirements in [`prd/001_initial_prd.md`](prd/001_initial_prd.md:1), the core system components for Project Scribe are:
*   **1.1. Configuration Manager**
    *   **Responsibilities:**
        *   Load operational parameters from the external `config.json` file ([`FR4.1.1`](prd/001_initial_prd.md:103)).
        *   Provide default values for configuration parameters if not specified ([`FR4.1.2`](prd/001_initial_prd.md:104)).
        *   Make configuration settings available to other components.
    *   **Interactions/Dependencies:**
        *   Accessed by most other components to retrieve settings.
*   **1.2. Directory Monitor**
    *   **Responsibilities:**
        *   Continuously monitor the configured `InputDirectoryName` (and subdirectories) for new files matching `SupportedInputFileExtensions` ([`FR4.2.1`](prd/001_initial_prd.md:107)).
        *   Detect new input files and trigger the processing pipeline.
    *   **Interactions/Dependencies:**
        *   Uses `Configuration Manager` for directory paths and file extensions.
        *   Initiates work for the `Book Processor`.
*   **1.3. Book Processor (Orchestrator)**
    *   **Responsibilities:**
        *   Manage the end-to-end processing workflow for a single book.
        *   Coordinate activities between `Input Parser`, `Text Processor`, `TTS Client`, and `Audio Post-Processor`.
        *   Maintain the directory structure for processed files ([`FR4.2.3`](prd/001_initial_prd.md:109)).
    *   **Interactions/Dependencies:**
        *   Receives new book notifications from `Directory Monitor`.
        *   Delegates tasks to specialized components.
        *   Interacts with `State Manager` to track and resume progress.
*   **1.4. Input Parser (e.g., ePub Parser)**
    *   **Responsibilities:**
        *   Parse the content of an input file (initially ePub) to extract individual chapters ([`FR4.3.1`](prd/001_initial_prd.md:112)).
        *   Identify chapter breaks and extract textual content ([`FR4.3.2`](prd/001_initial_prd.md:113)).
        *   Ignore non-textual content like images and tables ([`FR4.3.3`](prd/001_initial_prd.md:114)).
    *   **Interactions/Dependencies:**
        *   Receives file path from `Book Processor`.
        *   Provides extracted chapter text to `Text Processor`.
        *   Uses libraries like `EpubReader` ([`prd/001_initial_prd.md:199`](prd/001_initial_prd.md:199)).
*   **1.5. Text Processor**
    *   **Responsibilities:**
        *   Sanitize extracted text, removing problematic Unicode symbols ([`FR4.4.1`](prd/001_initial_prd.md:117)).
        *   Split chapter text into smaller blocks based on paragraph/sentence boundaries and `TextBlockTargetCharCount` ([`FR4.4.2`](prd/001_initial_prd.md:118), [`FR4.4.3`](prd/001_initial_prd.md:119), [`FR4.4.4`](prd/001_initial_prd.md:120)).
        *   Normalize block sizes ([`FR4.4.5`](prd/001_initial_prd.md:121)).
        *   Save processed text blocks as `.txt` files in `PrepareDirectoryName` ([`FR4.4.6`](prd/001_initial_prd.md:122)).
    *   **Interactions/Dependencies:**
        *   Receives chapter text from `Input Parser`.
        *   Provides text blocks to `TTS Client`.
        *   Uses `Configuration Manager` for `TextBlockTargetCharCount`.
*   **1.6. TTS Client (Gemini API Interaction)**
    *   **Responsibilities:**
        *   Send text blocks to the configured Gemini TTS API endpoint ([`FR4.5.1`](prd/001_initial_prd.md:125)).
        *   Include necessary parameters like voice, model, and API key in requests ([`FR4.5.2`](prd/001_initial_prd.md:126)).
        *   Manage concurrent TTS requests up to `MaxConcurrentTtsThreads` ([`FR4.5.3`](prd/001_initial_prd.md:127)).
        *   Save received WAV data to `WavDirectoryName` ([`FR4.5.4`](prd/001_initial_prd.md:128)).
        *   Verify received WAV files ([`FR4.5.5`](prd/001_initial_prd.md:129)).
        *   Implement retry logic with backoff for failed TTS requests ([`FR4.5.6`](prd/001_initial_prd.md:130), [`FR4.5.7`](prd/001_initial_prd.md:131)).
        *   Handle persistent failures and notify `Book Processor` ([`FR4.5.8`](prd/001_initial_prd.md:132)).
    *   **Interactions/Dependencies:**
        *   Receives text blocks from `Text Processor`.
        *   Uses `Configuration Manager` for API endpoint, voice, model, concurrency, and retry settings.
        *   Interacts with the external Gemini TTS service.
        *   Provides WAV file paths to `Audio Post-Processor`.
*   **1.7. Audio Post-Processor (ffmpeg Interaction)**
    *   **Responsibilities:**
        *   Concatenate WAV files in order, in batches up to `MaxAudioBatchDurationMinutes` ([`FR4.6.1`](prd/001_initial_prd.md:135)).
        *   Convert concatenated WAV audio to the `FinalAudioFormat` (e.g., Opus, MP3) using `ffmpeg` ([`FR4.6.2`](prd/001_initial_prd.md:136)).
        *   Apply `FinalAudioBitrate` ([`FR4.6.3`](prd/001_initial_prd.md:137)).
        *   Save final audio files to `OutputDirectoryName` ([`FR4.6.4`](prd/001_initial_prd.md:138)).
    *   **Interactions/Dependencies:**
        *   Receives WAV file paths from `TTS Client`.
        *   Uses `Configuration Manager` for format, bitrate, batch duration, and `ffmpeg` path.
        *   Interacts with the external `ffmpeg` tool.
*   **1.8. State Manager (SQLite Interaction)**
    *   **Responsibilities:**
        *   Manage application state, tracking processing progress per book/block using an SQLite database ([`FR4.7.1`](prd/001_initial_prd.md:141)).
        *   Enable resumability by querying the database on restart to identify and resume incomplete jobs ([`FR4.7.2`](prd/001_initial_prd.md:142)).
        *   Prevent reprocessing of already completed and valid steps ([`FR4.7.3`](prd/001_initial_prd.md:143)).
    *   **Interactions/Dependencies:**
        *   Interacts with `Book Processor` and other components involved in multi-step operations (e.g., `Text Processor`, `TTS Client`, `Audio Post-Processor`) to record and query progress.
        *   Uses `Microsoft.Data.Sqlite` ([`prd/001_initial_prd.md:200`](prd/001_initial_prd.md:200)).
*   **1.9. Logging Service**
    *   **Responsibilities:**
        *   Write detailed operational logs to a `/logs` subdirectory ([`FR4.8.1`](prd/001_initial_prd.md:146)).
        *   Separate logs by level (Trace, Debug, Info, Warn, Error) ([`FR4.8.2`](prd/001_initial_prd.md:147)).
        *   Support configurable log levels ([`FR4.8.3`](prd/001_initial_prd.md:148)).
        *   Ensure critical errors result in clear notifications ([`FR4.8.4`](prd/001_initial_prd.md:149)).
    *   **Interactions/Dependencies:**
        *   Used by all other components to log events, errors, and progress.
        *   Uses `Microsoft.Extensions.Logging` with Serilog provider ([`prd/001_initial_prd.md:203`](prd/001_initial_prd.md:203)).
        *   Uses `Configuration Manager` for `DefaultLogLevel`.
---
## 2. Architectural Patterns
*   **2.1. Recommended Pattern: Modular Monolith with Event-Driven Elements**
    *   **Modular Monolith:** The application will be a single deployable unit (`ProjectScribe.Console`), but its internal structure will be highly modular, with clear separation of concerns into different libraries/namespaces (e.g., `ProjectScribe.Logic` containing distinct services for parsing, text processing, TTS interaction, etc., as outlined in Section 6.1 of [`prd/001_initial_prd.md`](prd/001_initial_prd.md:192)). This aligns with [`PG2`](prd/001_initial_prd.md:71) (Maintainable and Extensible Architecture) and [`FR4.9.1`](prd/001_initial_prd.md:152) (Modularity).
        *   **Justification:**
            *   **Simplicity for V1.0:** For a console application primarily for personal use, a full microservices architecture would be overkill, increasing deployment and operational complexity unnecessarily.
            *   **Maintainability:** Clear module boundaries within the monolith, enforced by dependency injection (`SimpleInject` - [`prd/001_initial_prd.md:198`](prd/001_initial_prd.md:198)) and well-defined interfaces, will support maintainability ([`NFR5.3`](prd/001_initial_prd.md:168)) and testability ([`NFR5.4`](prd/001_initial_prd.md:175)).
            *   **Team Structure:** Suitable for a small team or single developer.
            *   **Performance:** Direct in-process communication between modules is efficient.
    *   **Event-Driven Elements (Internal):** While not a fully event-driven architecture with message brokers for this V1.0, certain interactions can be conceptualized or implemented using event-like mechanisms or callbacks, particularly:
        *   `Directory Monitor` detecting a new file can trigger a "new file detected" event/call to the `Book Processor`.
        *   Completion of stages within the `Book Processor` (e.g., text block processed, WAV file generated) can trigger the next stage.
        *   **Justification:**
            *   **Decoupling:** Promotes loose coupling between the file detection mechanism and the processing pipeline.
            *   **Asynchronous Operations:** Aligns well with the nature of file system monitoring and long-running TTS/audio processing tasks.
            *   **Resilience:** Helps in isolating failures; for example, a failure in processing one book doesn't necessarily halt the monitoring for new books.
*   **2.2. Layered Aspects:**
    *   Within the `ProjectScribe.Logic` module, a layered approach will be evident:
        *   **Application Services:** Orchestrate use cases (e.g., `BookProcessingService`).
        *   **Domain Services:** Encapsulate core business logic (e.g., `TextSplittingService`, `TtsRequestService`).
        *   **Infrastructure Services:** Handle external interactions (e.g., `FileSystemService`, `SqlitePersistenceService`, `GeminiApiClient`, `FfmpegClient`).
    *   **Justification:**
        *   **Separation of Concerns:** Enforces clear responsibilities, improving maintainability and testability.
        *   **Adherence to SOLID:** Particularly the Dependency Inversion Principle, as higher-level modules will depend on abstractions of lower-level modules.
---
## 3. Conceptual Data Flows
*   **3.1. New Book Ingestion and Text Preparation**
    1.  **User:** Places a new ePub file (e.g., `new_book.epub`) into a subdirectory of `InputDirectoryName`.
    2.  **Directory Monitor:** Detects `new_book.epub` via OS file system events.
    3.  **Directory Monitor:** Notifies `Book Processor` about the new file path.
    4.  **Book Processor:**
        *   Creates corresponding subdirectories in `PrepareDirectoryName`.
        *   Instructs `Input Parser` to process `new_book.epub`.
    5.  **Input Parser:**
        *   Reads `new_book.epub`.
        *   Extracts chapters and their text content.
        *   Passes chapter text sequentially to `Text Processor`.
    6.  **Text Processor:**
        *   For each chapter's text:
            *   Sanitizes text.
            *   Splits text into blocks (e.g., `0001.txt`, `0002.txt`, ...).
            *   Saves each block to the book's subdirectory in `PrepareDirectoryName`.
        *   Notifies `Book Processor` of prepared text blocks.
    7.  **State Manager:** `Book Processor`, `Input Parser`, and `Text Processor` update the state database with progress (e.g., file detected, chapter parsed, text block saved).
*   **3.2. TTS Conversion and WAV File Generation**
    1.  **Book Processor:** (Triggered by completion of text preparation for a book or resumption of an incomplete job)
        *   Identifies pending text blocks for TTS from `PrepareDirectoryName` (or via `State Manager`).
        *   Creates corresponding subdirectories in `WavDirectoryName`.
        *   Submits text blocks to `TTS Client` for conversion, respecting `MaxConcurrentTtsThreads`.
    2.  **TTS Client:**
        *   For each text block:
            *   Constructs API request (text, voice, model from `Configuration Manager`).
            *   Sends request to `GeminiTtsApiEndpoint`.
            *   Receives base64-encoded PCM audio data with sample rate header (`X-Sample-Rate`).
            *   Converts PCM to WAV format in memory:
                *   Decodes base64 to raw PCM bytes.
                *   Generates 44-byte WAV header with proper little-endian encoding.
                *   Combines WAV header + PCM data into memory stream.
            *   Saves WAV data as a `.wav` file (e.g., `0001.wav`) in the book's subdirectory in `WavDirectoryName`.
            *   Verifies WAV file.
            *   Handles retries for failures. If a block fails all retries, logs and notifies `Book Processor`.
        *   Notifies `Book Processor` of successfully generated (or failed) WAV files.
    3.  **State Manager:** `Book Processor` and `TTS Client` update the state database with progress (e.g., TTS request sent, WAV received, WAV verified).
*   **3.3. Audio Post-Processing and Final Output**
    1.  **Book Processor:** (Triggered by accumulation of WAV files for a book or resumption)
        *   Identifies processed `.wav` files in `WavDirectoryName` ready for concatenation/conversion (or via `State Manager`).
        *   Creates corresponding subdirectories in `OutputDirectoryName`.
        *   Groups WAV files into batches based on `MaxAudioBatchDurationMinutes`.
        *   Instructs `Audio Post-Processor` to process each batch.
    2.  **Audio Post-Processor:**
        *   For each batch of WAV files:
            *   Concatenates them in order.
            *   Uses `ffmpeg` to convert the concatenated audio to `FinalAudioFormat` with `FinalAudioBitrate`.
            *   Saves the final audio file (e.g., `book_name_part001.opus`) to the book's subdirectory in `OutputDirectoryName`.
        *   Notifies `Book Processor` of successfully generated final audio files.
    3.  **State Manager:** `Book Processor` and `Audio Post-Processor` update the state database with progress (e.g., WAV batch processed, final audio file saved).
---
## 4. Technology Stack Implications (High-Level)
The PRD ([`prd/001_initial_prd.md:197-204`](prd/001_initial_prd.md:197)) already specifies key technologies:
*   **Backend Language/Framework:** C# 9.0 with .NET.
    *   **Rationale:** Specified in PRD. Modern, robust, good performance, strong ecosystem.
    *   **Trade-offs:** Windows-centric for console deployment (though .NET Core/.NET 5+ are cross-platform, the PRD implies a Windows console app).
*   **Database Type(s):** SQLite.
    *   **Rationale:** Specified in PRD ([`FR4.7.1`](prd/001_initial_prd.md:141), [`prd/001_initial_prd.md:200`](prd/001_initial_prd.md:200)). Lightweight, serverless, file-based, suitable for single-user application state management and resumability.
    *   **Trade-offs:** Not designed for high concurrency from multiple applications (but fine for this single-instance app). Limited tooling compared to server-based RDBMS.
*   **Dependency Injection:** `SimpleInject`.
    *   **Rationale:** Specified in PRD ([`prd/001_initial_prd.md:198`](prd/001_initial_prd.md:198)). Helps achieve loose coupling and testability.
    *   **Trade-offs:** Learning curve if unfamiliar. Adds a dependency.
*   **ePub Parsing:** `EpubReader` library.
    *   **Rationale:** Specified in PRD ([`FR4.3.1`](prd/001_initial_prd.md:112), [`prd/001_initial_prd.md:199`](prd/001_initial_prd.md:199)). Provides necessary ePub parsing capabilities.
    *   **Trade-offs:** Dependency on an external library; its capabilities and limitations will affect parsing quality.
*   **Audio Conversion:** `ffmpeg` (external tool).
    *   **Rationale:** Specified in PRD ([`FR4.6.2`](prd/001_initial_prd.md:136), [`prd/001_initial_prd.md:201`](prd/001_initial_prd.md:201)). Powerful and widely used for audio/video manipulation.
    *   **Trade-offs:** External dependency that needs to be available on the system or bundled. Invoking as an external process adds slight overhead.
*   **Logging:** `Microsoft.Extensions.Logging` with Serilog provider.
    *   **Rationale:** Specified in PRD ([`prd/001_initial_prd.md:203`](prd/001_initial_prd.md:203)). Flexible, structured logging.
    *   **Trade-offs:** Configuration complexity can be higher than simpler logging mechanisms.
*   **API Key Management:** `.env` file.
    *   **Rationale:** Specified in PRD ([`prd/001_initial_prd.md:204`](prd/001_initial_prd.md:204)). Standard practice for keeping secrets out of source control.
    *   **Trade-offs:** Requires user to manage the `.env` file correctly.
---
## 5. Non-Functional Requirements (NFRs) Support
*   **5.1. Scalability (Load: Number of books, chapter sizes, concurrent processing)**
    *   **Concurrent TTS Processing:** The `MaxConcurrentTtsThreads` parameter ([`FR4.5.3`](prd/001_initial_prd.md:127)) allows scaling the number of parallel TTS requests, directly impacting throughput ([`NFR5.1.1`](prd/001_initial_prd.md:159)).
    *   **Asynchronous Operations:** The overall pipeline (directory monitoring, per-book processing) can operate asynchronously, allowing the system to handle multiple books over time without blocking.
    *   **Modular Design:** Allows for future optimization of specific components if they become bottlenecks.
    *   **Limitations:** As a single console application, scalability is primarily vertical (better hardware) or through the configured concurrency. It's not designed for distributed scaling.
*   **5.2. Security (Primarily API Key Protection)**
    *   **API Key Management:** Loading the Gemini TTS API key from an `.env` file ([`prd/001_initial_prd.md:204`](prd/001_initial_prd.md:204)) keeps it out of source control.
    *   **Local Operation:** The application runs locally, reducing exposure to network-based attacks on the application itself (TTS API interaction is an outgoing connection).
    *   **Input Sanitization:** Basic text sanitization ([`FR4.4.1`](prd/001_initial_prd.md:117)) can prevent issues with TTS processing, though not primarily a security feature against malicious ePubs.
    *   **Considerations:** Ensure file paths from configuration are handled safely to prevent path traversal issues if paths were ever user-controllable beyond the config file.
*   **5.3. Performance**
    *   **Concurrent TTS:** Key to achieving the ~5x real-time target ([`NFR5.1.1`](prd/001_initial_prd.md:159)).
    *   **Efficient File I/O:** Using OS-level file system events for directory monitoring ([`FR4.2.1`](prd/001_initial_prd.md:107), [`NFR5.1.2`](prd/001_initial_prd.md:160)) is more efficient than polling.
    *   **Batch Processing:** Audio concatenation and conversion in batches ([`FR4.6.1`](prd/001_initial_prd.md:135)) can be more efficient than processing many tiny files individually with `ffmpeg`.
    *   **SQLite Optimization:** Efficient queries and indexing (if complex queries arise) for state management ([`NFR5.1.3`](prd/001_initial_prd.md:161)).
*   **5.4. Fault Tolerance & Resilience**
    *   **State Management & Resumability:** SQLite database for tracking progress ([`FR4.7.1`](prd/001_initial_prd.md:141)) allows resuming from the last known good state after interruptions ([`FR4.7.2`](prd/001_initial_prd.md:142), [`PG4`](prd/001_initial_prd.md:73), [`NFR5.2.1`](prd/001_initial_prd.md:164)).
    *   **TTS Retries:** Robust retry mechanism with backoff for TTS API requests ([`FR4.5.6`](prd/001_initial_prd.md:130), [`FR4.5.7`](prd/001_initial_prd.md:131), [`NFR5.2.2`](prd/001_initial_prd.md:165)).
    *   **Error Handling:** Graceful handling of persistent TTS failures for a block, allowing other blocks/books to proceed or the book to be paused ([`FR4.5.8`](prd/001_initial_prd.md:132)).
    *   **Logging:** Detailed logging ([`FR4.8`](prd/001_initial_prd.md:145)) aids in diagnosing and recovering from failures.
    *   **Modular Isolation:** Failures in one module (e.g., parsing a specific malformed ePub) are less likely to bring down the entire application, especially the directory monitoring part.
---
## 6. Diagrammatic Representation Suggestions
Diagrams will use Mermaid.js syntax as per [`prompts/50_mermaid_rules.md`](prompts/50_mermaid_rules.md:1).
*   **6.1. Component Diagram**
    *   **Purpose:** To illustrate the core system components identified in Section 1 and their primary relationships (dependencies and communication paths).
    *   **Mermaid Pseudo-code:**
        ```mermaid
        graph TD;
            User["User (Filesystem)"];
            ConfigManager["Configuration Manager"];
            DirectoryMonitor["Directory Monitor"];
            BookProcessor["Book Processor (Orchestrator)"];
            InputParser["Input Parser (e.g., ePub)"];
            TextProcessor["Text Processor"];
            TTSClient["TTS Client (Gemini API)"];
            AudioPostProcessor["Audio Post-Processor (ffmpeg)"];
            StateManager["State Manager (SQLite)"];
            LoggingService["Logging Service"];
            External_GeminiAPI["External: Gemini TTS API"];
            External_ffmpeg["External: ffmpeg Tool"];
            User --> DirectoryMonitor;
            DirectoryMonitor --> BookProcessor;
            BookProcessor --> InputParser;
            BookProcessor --> TextProcessor;
            BookProcessor --> TTSClient;
            BookProcessor --> AudioPostProcessor;
            BookProcessor --> StateManager;
            InputParser --> TextProcessor;
            TextProcessor --> TTSClient;
            TTSClient --> AudioPostProcessor;
            TTSClient -- "HTTP Requests" --> External_GeminiAPI;
            AudioPostProcessor -- "CLI Calls" --> External_ffmpeg;
            ConfigManager -- "Provides Settings" --> DirectoryMonitor;
            ConfigManager -- "Provides Settings" --> BookProcessor;
            ConfigManager -- "Provides Settings" --> TextProcessor;
            ConfigManager -- "Provides Settings" --> TTSClient;
            ConfigManager -- "Provides Settings" --> AudioPostProcessor;
            ConfigManager -- "Provides Settings" --> LoggingService;
            StateManager -- "DB Operations" --> BookProcessor;
            StateManager -- "DB Operations" --> InputParser;
            StateManager -- "DB Operations" --> TextProcessor;
            StateManager -- "DB Operations" --> TTSClient;
            StateManager -- "DB Operations" --> AudioPostProcessor;
            LoggingService -- "Used by" --> DirectoryMonitor;
            LoggingService -- "Used by" --> BookProcessor;
            LoggingService -- "Used by" --> InputParser;
            LoggingService -- "Used by" --> TextProcessor;
            LoggingService -- "Used by" --> TTSClient;
            LoggingService -- "Used by" --> AudioPostProcessor;
            LoggingService -- "Used by" --> StateManager;
            LoggingService -- "Used by" --> ConfigManager;
        ```
*   **6.2. High-Level Conceptual Architecture Diagram (C4 Model - Level 2 "Containers" adapted for a Monolith)**
    *   **Purpose:** To show the overall structure of Project Scribe as a single application (the "System") interacting with the user (via the file system) and external services. It will highlight the main internal logical modules (our "containers" in this adapted C4 context) within the monolithic application.
    *   **Mermaid Pseudo-code:**
        ```mermaid
        graph TD;
            User["User (Interacts via File System)"];
            ProjectScribeSystem["Project Scribe Application (Windows Console App)"];
            InputOutputSubsystem["Input/Output Subsystem"];
            CoreProcessingLogic["Core Processing Logic"];
            ExternalServicesInterface["External Services Interface"];
            GeminiTTS_Service["External: Gemini TTS API"];
            ffmpeg_Tool["External: ffmpeg Tool"];
            SQLite_DB["SQLite Database (Local File)"];
            ConfigurationFile["config.json (Local File)"];
            LogFiles["Log Files (Local Files)"];
            User -- "Places/Retrieves Files" --> ProjectScribeSystem;
            ProjectScribeSystem -- "Contains" --> InputOutputSubsystem;
            ProjectScribeSystem -- "Contains" --> CoreProcessingLogic;
            ProjectScribeSystem -- "Contains" --> ExternalServicesInterface;
            InputOutputSubsystem -- "Manages" --> ConfigurationFile;
            InputOutputSubsystem -- "Writes to" --> LogFiles;
            InputOutputSubsystem -- "Triggers" --> CoreProcessingLogic;
            CoreProcessingLogic -- "Uses" --> InputOutputSubsystem;
            CoreProcessingLogic -- "Manages State via" --> SQLite_DB;
            CoreProcessingLogic -- "Delegates to" --> ExternalServicesInterface;
            ExternalServicesInterface -- "Calls" --> GeminiTTS_Service;
            ExternalServicesInterface -- "Executes" --> ffmpeg_Tool;
            ProjectScribeSystem -- "Accesses" --> SQLite_DB;