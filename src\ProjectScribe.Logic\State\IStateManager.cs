using ProjectScribe.Logic.State.Entities;

namespace ProjectScribe.Logic.State
{
    /// <summary>
    /// Interface for managing application state and tracking processing progress.
    /// Provides methods for book and text block state management with resumability support.
    /// </summary>
    public interface IStateManager
    {
        #region Book State Management

        /// <summary>
        /// Initializes a new book in the state database.
        /// Creates a new BookProcessState record with Pending status.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <param name="filePath">Path to the original ePub file.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        Task InitializeBookAsync(string bookId, string filePath);

        /// <summary>
        /// Retrieves the current state of a book.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>BookProcessState if found, null otherwise.</returns>
        Task<BookProcessState?> GetBookStateAsync(string bookId);

        /// <summary>
        /// Retrieves all books that have incomplete processing.
        /// Returns books that are not in 'Completed' or 'Failed' status.
        /// </summary>
        /// <returns>Collection of incomplete book states for resumability.</returns>
        Task<IEnumerable<BookProcessState>> GetIncompleteBooksAsync();

        /// <summary>
        /// Updates the status and processing step of a book.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <param name="status">New processing status.</param>
        /// <param name="lastStep">Description of the last completed step.</param>
        /// <param name="errorMessage">Error message if applicable.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        Task UpdateBookStatusAsync(string bookId, BookProcessStatus status, string? lastStep = null, string? errorMessage = null);

        /// <summary>
        /// Checks if a book exists in the state database.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>True if the book exists, false otherwise.</returns>
        Task<bool> BookExistsAsync(string bookId);

        #endregion

        #region Text Block State Management

        /// <summary>
        /// Adds a new text block to the state database.
        /// </summary>
        /// <param name="textBlock">Text block state to add.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        Task AddTextBlockAsync(TextBlockState textBlock);

        /// <summary>
        /// Adds multiple text blocks to the state database in a single transaction.
        /// </summary>
        /// <param name="textBlocks">Collection of text block states to add.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        Task AddTextBlocksAsync(IEnumerable<TextBlockState> textBlocks);

        /// <summary>
        /// Retrieves all text blocks for a specific book that have the specified status.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <param name="status">Status to filter by.</param>
        /// <returns>Collection of text block states matching the criteria.</returns>
        Task<IEnumerable<TextBlockState>> GetTextBlocksForBookByStatusAsync(string bookId, TextBlockStatus status);

        /// <summary>
        /// Retrieves all text blocks for a specific book, ordered by sequence number.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>Collection of text block states ordered by sequence.</returns>
        Task<IEnumerable<TextBlockState>> GetTextBlocksForBookAsync(string bookId);

        /// <summary>
        /// Retrieves text blocks that are pending TTS processing for a specific book.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>Collection of text block states pending TTS processing.</returns>
        Task<IEnumerable<TextBlockState>> GetPendingTextBlocksAsync(string bookId);

        /// <summary>
        /// Updates the status and related properties of a text block.
        /// </summary>
        /// <param name="textBlockId">Unique identifier for the text block.</param>
        /// <param name="status">New processing status.</param>
        /// <param name="wavPath">Path to the generated WAV file (if applicable).</param>
        /// <param name="retryCount">Updated retry count (if applicable).</param>
        /// <param name="errorMessage">Error message (if applicable).</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        Task UpdateTextBlockStatusAsync(string textBlockId, TextBlockStatus status, string? wavPath = null, int? retryCount = null, string? errorMessage = null);

        /// <summary>
        /// Retrieves a specific text block by its identifier.
        /// </summary>
        /// <param name="textBlockId">Unique identifier for the text block.</param>
        /// <returns>TextBlockState if found, null otherwise.</returns>
        Task<TextBlockState?> GetTextBlockAsync(string textBlockId);

        /// <summary>
        /// Gets the count of text blocks for a book by status.
        /// Useful for progress tracking and reporting.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <param name="status">Status to count.</param>
        /// <returns>Number of text blocks with the specified status.</returns>
        Task<int> GetTextBlockCountByStatusAsync(string bookId, TextBlockStatus status);

        /// <summary>
        /// Resets text blocks that are stuck in TTS_InProgress status back to PendingTTS.
        /// This handles cases where processing was interrupted and blocks were left in an in-progress state.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>Number of blocks that were reset.</returns>
        Task<int> ResetInProgressTextBlocksAsync(string bookId);

        /// <summary>
        /// Resets text blocks that are in TTS_FailedPermanent status back to PendingTTS.
        /// This allows permanently failed blocks to be retried on application restart.
        /// </summary>
        /// <param name="bookId">Unique identifier for the book.</param>
        /// <returns>Number of blocks that were reset.</returns>
        Task<int> ResetPermanentlyFailedTextBlocksAsync(string bookId);

        #endregion

        #region Database Management

        /// <summary>
        /// Ensures the database is created and migrations are applied.
        /// Should be called during application startup.
        /// </summary>
        /// <returns>Task representing the asynchronous operation.</returns>
        Task InitializeDatabaseAsync();

        #endregion
    }
}
