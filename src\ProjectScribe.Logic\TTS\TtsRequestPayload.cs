using System.Text.Json.Serialization;

namespace ProjectScribe.Logic.TTS
{
    /// <summary>
    /// Represents the JSON payload for TTS API requests.
    /// </summary>
    public class TtsRequestPayload
    {
        /// <summary>
        /// The text to convert to speech.
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// The TTS model to use for generation.
        /// </summary>
        [JsonPropertyName("model")]
        public string Model { get; set; } = string.Empty;
    }
}
