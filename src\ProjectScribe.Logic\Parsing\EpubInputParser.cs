using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using ProjectScribe.Logic.Parsing.Models;
using System.IO.Compression;
using System.Text;

namespace ProjectScribe.Logic.Parsing
{
    /// <summary>
    /// Implementation of IInputParser for EPUB files.
    /// Uses a custom ZIP-based parser to extract text content from EPUB files.
    /// </summary>
    public class EpubInputParser : IInputParser
    {
        private readonly ILogger<EpubInputParser> _logger;

        /// <summary>
        /// Initializes a new instance of the EpubInputParser class.
        /// </summary>
        /// <param name="logger">Logger for recording parsing operations and errors.</param>
        public EpubInputParser(ILogger<EpubInputParser> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Parses an EPUB file and extracts chapter content.
        /// </summary>
        /// <param name="filePath">Full path to the EPUB file to parse.</param>
        /// <returns>A collection of ChapterContent objects representing the chapters in the EPUB.</returns>
        /// <exception cref="ArgumentException">Thrown when the file path is null or empty.</exception>
        /// <exception cref="FileNotFoundException">Thrown when the specified file does not exist.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the EPUB file is corrupted or cannot be parsed.</exception>
        public async Task<IEnumerable<ChapterContent>> ParseAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                throw new ArgumentException("File path cannot be null or empty.", nameof(filePath));
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"EPUB file not found: {filePath}");
            }

            _logger.LogInformation("Starting EPUB parsing for file: {FilePath}", filePath);

            try
            {
                var chapters = new List<ChapterContent>();

                // Open EPUB file as ZIP archive
                using var archive = ZipFile.OpenRead(filePath);

                _logger.LogInformation("Successfully opened EPUB file as ZIP archive: {FilePath}", filePath);

                // Find all HTML/XHTML content files
                var contentFiles = GetContentFiles(archive);

                _logger.LogDebug("Found {FileCount} content files in EPUB", contentFiles.Count);

                int chapterNumber = 1;

                // Process each content file in order
                foreach (var entry in contentFiles)
                {
                    try
                    {
                        // Read HTML content from ZIP entry
                        string htmlContent;
                        try
                        {
                            using var stream = entry.Open();
                            using var reader = new StreamReader(stream);
                            htmlContent = await reader.ReadToEndAsync();
                        }
                        catch (Exception contentEx)
                        {
                            _logger.LogWarning(contentEx, "Failed to read content for chapter {ChapterNumber} from file {FilePath}. Skipping chapter.",
                                chapterNumber, entry.FullName);
                            continue;
                        }

                        var chapterContent = ExtractTextFromHtml(htmlContent, chapterNumber);

                        // Only add chapters that have meaningful content
                        if (!string.IsNullOrWhiteSpace(chapterContent.TextContent))
                        {
                            chapters.Add(chapterContent);
                            _logger.LogDebug("Extracted chapter {ChapterNumber}: {Title} ({CharCount} characters)",
                                chapterNumber, chapterContent.Title, chapterContent.TextContent.Length);
                            chapterNumber++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to extract content from chapter {ChapterNumber} in file {FilePath}. Skipping chapter.",
                            chapterNumber, entry.FullName);
                        // Continue processing other chapters
                    }
                }

                // Filter out metadata chapters from the beginning and end
                var filteredChapters = FilterMetadataChapters(chapters);

                _logger.LogInformation("Successfully parsed EPUB file. Extracted {ChapterCount} chapters from {FilePath} (filtered from {OriginalCount})",
                    filteredChapters.Count, filePath, chapters.Count);

                return filteredChapters;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse EPUB file: {FilePath}", filePath);
                throw new InvalidOperationException($"Failed to parse EPUB file: {filePath}", ex);
            }
        }

        /// <summary>
        /// Extracts plain text content from HTML using HtmlAgilityPack.
        /// Removes HTML markup and focuses on readable text content.
        /// </summary>
        /// <param name="htmlContent">The HTML content to process.</param>
        /// <param name="chapterNumber">The chapter number for this content.</param>
        /// <returns>A ChapterContent object with extracted text.</returns>
        private ChapterContent ExtractTextFromHtml(string htmlContent, int chapterNumber)
        {
            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(htmlContent);

            var chapterContent = new ChapterContent
            {
                ChapterNumber = chapterNumber
            };

            // Try to extract chapter title from common heading tags
            var titleNode = htmlDocument.DocumentNode.SelectSingleNode("//h1 | //h2 | //h3 | //title");
            if (titleNode != null)
            {
                chapterContent.Title = CleanText(titleNode.InnerText);
            }

            // Extract all text content, excluding script and style elements
            var textNodes = htmlDocument.DocumentNode.SelectNodes("//text()[not(ancestor::script) and not(ancestor::style)]");
            
            if (textNodes != null)
            {
                var textBuilder = new StringBuilder();
                
                foreach (var textNode in textNodes)
                {
                    var text = CleanText(textNode.InnerText);
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        textBuilder.AppendLine(text);
                    }
                }

                chapterContent.TextContent = textBuilder.ToString().Trim();
            }

            return chapterContent;
        }

        /// <summary>
        /// Cleans and normalizes text content by removing extra whitespace and problematic characters.
        /// </summary>
        /// <param name="text">The text to clean.</param>
        /// <returns>Cleaned text suitable for TTS processing.</returns>
        private static string CleanText(string? text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return string.Empty;
            }

            // Decode HTML entities
            var decodedText = HtmlEntity.DeEntitize(text) ?? text;

            // Remove excessive whitespace and normalize line breaks
            var cleanedText = System.Text.RegularExpressions.Regex.Replace(decodedText, @"\s+", " ");

            return cleanedText.Trim();
        }

        /// <summary>
        /// Filters out metadata chapters from the beginning and end of the chapter list.
        /// Only removes metadata chapters from the first 2 and last 2 chapters to avoid
        /// removing legitimate story content from the middle of the book.
        /// </summary>
        /// <param name="chapters">The list of all extracted chapters.</param>
        /// <returns>A filtered list with metadata chapters removed from beginning and end.</returns>
        private List<ChapterContent> FilterMetadataChapters(List<ChapterContent> chapters)
        {
            if (chapters.Count == 0)
            {
                return chapters;
            }

            var filteredChapters = new List<ChapterContent>();
            const int maxMetadataChaptersToCheck = 2; // Only check first/last 2 chapters

            // Process chapters and renumber them
            int newChapterNumber = 1;

            for (int i = 0; i < chapters.Count; i++)
            {
                var chapter = chapters[i];
                bool isInMetadataZone = i < maxMetadataChaptersToCheck || i >= chapters.Count - maxMetadataChaptersToCheck;

                // Only apply metadata filtering to chapters at the beginning or end
                if (isInMetadataZone && IsMetadataChapter(chapter))
                {
                    _logger.LogDebug("Skipped metadata chapter at position {Position}: {Title} ({CharCount} characters)",
                        i + 1, chapter.Title, chapter.TextContent.Length);
                    continue;
                }

                // Keep the chapter and renumber it
                chapter.ChapterNumber = newChapterNumber++;
                filteredChapters.Add(chapter);
            }

            return filteredChapters;
        }

        /// <summary>
        /// Determines if a chapter contains mostly metadata, copyright, or administrative information.
        /// </summary>
        /// <param name="chapterContent">The chapter content to analyze.</param>
        /// <returns>True if the chapter appears to be metadata/info content, false otherwise.</returns>
        private bool IsMetadataChapter(ChapterContent chapterContent)
        {
            var text = chapterContent.TextContent.ToLowerInvariant();
            var title = chapterContent.Title.ToLowerInvariant();

            // Check title for common metadata indicators
            if (title.Contains("information") || title.Contains("table of contents") ||
                title.Contains("copyright") || title.Contains("about") ||
                title.Contains("disclaimer") || title.Contains("warning") ||
                title.Contains("toc") || title.Contains("index") ||
                title.Contains("acknowledgment") || title.Contains("preface"))
            {
                return !title.Contains("chapter");
            }

            // Count metadata indicators in the text
            var metadataIndicators = new[]
            {
                "table of contents", "url:", "amazon", "audible", "kindle",
                "discord", "patreon", "royalroad", "editor:", "original", "stub",
                "warning", "profanity", "sensitive content", "moved to kindle",
                "removed", "here it is on", "copyright",
                "all rights reserved", "published by", "isbn", "edition",
                "www.", "http", "https", "discord link", "support the author",
                "subscribe", "follow me", "social media"
            };

            var indicatorCount = metadataIndicators.Count(indicator => text.Contains(indicator));

            // If the chapter is short and has many metadata indicators, it's likely metadata
            if (chapterContent.TextContent.Length < 2000 && indicatorCount >= 3)
            {
                return true;
            }

            // Check for chapters that are mostly URLs, links, or administrative text
            var lines = chapterContent.TextContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var urlOrAdminLines = lines.Count(line =>
            {
                var lowerLine = line.ToLowerInvariant().Trim();
                return lowerLine.Contains("http") || lowerLine.Contains("www.") ||
                       lowerLine.Contains("amazon") || lowerLine.Contains("audible") ||
                       lowerLine.Contains("kindle") || lowerLine.Contains("discord") ||
                       lowerLine.Length < 10; // Very short lines are often formatting
            });

            // If more than 30% of lines are URLs or admin content, skip the chapter
            if (lines.Length > 0 && (double)urlOrAdminLines / lines.Length > 0.3)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Finds all HTML/XHTML content files in the EPUB archive.
        /// Returns files in alphabetical order for consistent chapter ordering.
        /// </summary>
        /// <param name="archive">The ZIP archive representing the EPUB file.</param>
        /// <returns>A list of ZIP entries containing HTML/XHTML content files.</returns>
        private List<ZipArchiveEntry> GetContentFiles(ZipArchive archive)
        {
            var contentFiles = new List<ZipArchiveEntry>();

            foreach (var entry in archive.Entries)
            {
                // Skip directories
                if (string.IsNullOrEmpty(entry.Name))
                    continue;

                // Look for HTML/XHTML files
                var extension = Path.GetExtension(entry.Name).ToLowerInvariant();
                if (extension == ".html" || extension == ".xhtml" || extension == ".htm")
                {
                    // Skip common non-content files
                    var fileName = entry.Name.ToLowerInvariant();
                    if (fileName.Contains("toc") || fileName.Contains("nav") ||
                        fileName.Contains("cover") || fileName.Contains("title"))
                    {
                        _logger.LogDebug("Skipping non-content file: {FileName}", entry.FullName);
                        continue;
                    }

                    contentFiles.Add(entry);
                    _logger.LogDebug("Found content file: {FileName}", entry.FullName);
                }
            }

            // Sort files alphabetically for consistent ordering
            contentFiles.Sort((a, b) => string.Compare(a.FullName, b.FullName, StringComparison.OrdinalIgnoreCase));

            return contentFiles;
        }
    }
}
