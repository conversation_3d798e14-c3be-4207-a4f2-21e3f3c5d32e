namespace ProjectScribe.Logic.Processing
{
    /// <summary>
    /// Interface for orchestrating the complete processing of a book from parsing through TTS conversion.
    /// Handles the end-to-end workflow of converting input files to audio output.
    /// </summary>
    public interface IBookProcessor
    {
        /// <summary>
        /// Processes a book file through the complete pipeline: parsing, text processing, and TTS conversion.
        /// Creates necessary directory structures, manages state, and handles error recovery.
        /// </summary>
        /// <param name="filePath">Full path to the input book file (e.g., .epub file).</param>
        /// <returns>Task representing the asynchronous processing operation.</returns>
        /// <exception cref="ArgumentException">Thrown when the file path is null, empty, or the file doesn't exist.</exception>
        /// <exception cref="InvalidOperationException">Thrown when critical processing steps fail.</exception>
        Task ProcessBookAsync(string filePath);
    }
}
